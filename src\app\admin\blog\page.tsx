/**
 * Admin Blog Management Page
 * 
 * Provides comprehensive blog post management including listing, filtering,
 * bulk operations, and quick access to create/edit functionality.
 */

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Tag,
  MoreHorizontal,
  FileText,
  Globe,
  Clock,
} from 'lucide-react'
import Link from 'next/link'
import { format } from 'date-fns'
import { AdminLayout } from '@/components/admin/admin-layout'
import { DataTable } from '@/components/admin/data-table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useApi, useApiMutation } from '@/hooks/use-api'
import { toast } from 'react-hot-toast'
import type { IBlogPost } from '@/types'

interface BlogPostTableData extends IBlogPost {
  _id: string
  title: string
  slug: string
  excerpt?: string
  published: boolean
  publishedAt?: Date
  createdAt: Date
  updatedAt: Date
  author?: string
  tags: string[]
  views?: number
}

/**
 * Admin Blog Posts Page Component
 */
export default function AdminBlogPage(): JSX.Element {
  const [selectedPosts, setSelectedPosts] = useState<BlogPostTableData[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft'>('all')
  
  // API hooks
  const { data: blogData, loading, error, refetch } = useApi<{
    posts: BlogPostTableData[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }>('/api/blog?admin=true')
  
  const { mutate: deletePost, loading: deleteLoading } = useApiMutation()
  const { mutate: togglePublish, loading: publishLoading } = useApiMutation()

  // Handle row selection
  const handleRowSelect = (posts: BlogPostTableData[]) => {
    setSelectedPosts(posts)
  }

  // Handle single post deletion
  const handleDeletePost = async (postId: string) => {
    if (!confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
      return
    }

    try {
      await deletePost(`/api/blog/${postId}`, {}, 'DELETE')
      toast.success('Blog post deleted successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to delete blog post')
    }
  }

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedPosts.length} blog posts? This action cannot be undone.`)) {
      return
    }

    try {
      await Promise.all(
        selectedPosts.map(post => 
          deletePost(`/api/blog/${post.slug}`, {}, 'DELETE')
        )
      )
      toast.success(`${selectedPosts.length} blog posts deleted successfully`)
      setSelectedPosts([])
      refetch()
    } catch (error) {
      toast.error('Failed to delete some blog posts')
    }
  }

  // Handle publish/unpublish toggle
  const handleTogglePublish = async (post: BlogPostTableData) => {
    try {
      await togglePublish(`/api/blog/${post.slug}`, {
        published: !post.published,
        publishedAt: !post.published ? new Date() : null
      }, 'PUT')
      
      toast.success(`Blog post ${!post.published ? 'published' : 'unpublished'} successfully`)
      refetch()
    } catch (error) {
      toast.error('Failed to update blog post status')
    }
  }

  // Table columns configuration
  const columns = [
    {
      key: 'title',
      label: 'Title',
      render: (post: BlogPostTableData) => (
        <div className="space-y-1">
          <div className="font-medium">{post.title}</div>
          {post.excerpt && (
            <div className="text-sm text-muted-foreground line-clamp-2">
              {post.excerpt}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (post: BlogPostTableData) => (
        <Badge variant={post.published ? 'default' : 'secondary'}>
          {post.published ? 'Published' : 'Draft'}
        </Badge>
      ),
    },
    {
      key: 'publishedAt',
      label: 'Published',
      render: (post: BlogPostTableData) => (
        <div className="text-sm">
          {post.published && post.publishedAt ? (
            <div className="space-y-1">
              <div>{format(new Date(post.publishedAt), 'MMM dd, yyyy')}</div>
              <div className="text-muted-foreground">{format(new Date(post.publishedAt), 'HH:mm')}</div>
            </div>
          ) : (
            <span className="text-muted-foreground">Not published</span>
          )}
        </div>
      ),
    },
    {
      key: 'tags',
      label: 'Tags',
      render: (post: BlogPostTableData) => (
        <div className="flex flex-wrap gap-1">
          {post.tags?.slice(0, 2).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {post.tags?.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{post.tags.length - 2}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'views',
      label: 'Views',
      render: (post: BlogPostTableData) => (
        <div className="flex items-center gap-1 text-sm">
          <Eye className="h-3 w-3" />
          {post.views || 0}
        </div>
      ),
    },
  ]

  // Table actions configuration
  const actions = [
    {
      label: 'View',
      icon: Eye,
      onClick: (post: BlogPostTableData) => {
        window.open(`/blog/${post.slug}`, '_blank')
      },
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: (post: BlogPostTableData) => {
        window.location.href = `/admin/blog/${post.slug}/edit`
      },
    },
    {
      label: post => post.published ? 'Unpublish' : 'Publish',
      icon: Globe,
      onClick: handleTogglePublish,
      loading: publishLoading,
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (post: BlogPostTableData) => handleDeletePost(post.slug),
      variant: 'destructive' as const,
      loading: deleteLoading,
    },
  ]

  // Filter posts based on search and status
  const filteredPosts = blogData?.posts?.filter(post => {
    const matchesSearch = !searchQuery ||
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'published' && post.published) ||
      (statusFilter === 'draft' && !post.published)

    return matchesSearch && matchesStatus
  }) || []

  return (
    <AdminLayout
      title="Blog Posts"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Blog Posts' },
      ]}
    >
      <div className="space-y-6">
        {/* Header with Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid gap-4 md:grid-cols-4"
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{blogData?.posts?.length || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Published</CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {blogData?.posts?.filter(p => p.published).length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Drafts</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {blogData?.posts?.filter(p => !p.published).length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {blogData?.posts?.reduce((sum, p) => sum + (p.views || 0), 0) || 0}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Header Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex items-center justify-between"
        >
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Blog Posts</h2>
            <p className="text-muted-foreground">
              Create and manage your blog content
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {selectedPosts.length > 0 && (
              <Button
                variant="destructive"
                onClick={handleBulkDelete}
                disabled={deleteLoading}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected ({selectedPosts.length})
              </Button>
            )}
            <Button asChild>
              <Link href="/admin/blog/new">
                <Plus className="h-4 w-4 mr-2" />
                New Post
              </Link>
            </Button>
          </div>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex items-center space-x-4"
        >
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search posts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Status: {statusFilter === 'all' ? 'All' : statusFilter === 'published' ? 'Published' : 'Draft'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                All Posts
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('published')}>
                Published
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('draft')}>
                Drafts
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </motion.div>

        {/* Blog Posts Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <DataTable
            data={filteredPosts}
            columns={columns}
            loading={loading}
            error={error}
            pagination={blogData?.pagination}
            actions={actions}
            onRowSelect={handleRowSelect}
            onRowClick={(post) => {
              window.location.href = `/admin/blog/${post.slug}/edit`
            }}
          />
        </motion.div>
      </div>
    </AdminLayout>
  )
}
