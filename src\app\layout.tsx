import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "react-hot-toast";
import { ThemeProvider } from "@/components/providers/theme-provider";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Seyi Obadeyi - Full Stack Developer",
    template: "%s | Seyi Obadeyi",
  },
  description: "Full Stack Developer passionate about creating exceptional digital experiences with modern technologies and clean, efficient code.",
  keywords: [
    "Full Stack Developer",
    "React",
    "Next.js",
    "Node.js",
    "TypeScript",
    "MongoDB",
    "Web Development",
    "Software Engineer",
    "Seyi Obadeyi",
  ],
  authors: [{ name: "<PERSON><PERSON>badey<PERSON>", url: "https://seyi.obadeyi.com" }],
  creator: "<PERSON><PERSON> Obadeyi",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://seyi.obadeyi.com",
    title: "<PERSON><PERSON> Obadeyi - Full Stack Developer",
    description: "Full Stack Developer passionate about creating exceptional digital experiences with modern technologies and clean, efficient code.",
    siteName: "<PERSON><PERSON> Obadeyi Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Seyi Obadeyi - Full Stack Developer",
    description: "Full Stack Developer passionate about creating exceptional digital experiences with modern technologies and clean, efficient code.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster
            position="bottom-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'hsl(var(--card))',
                color: 'hsl(var(--card-foreground))',
                border: '1px solid hsl(var(--border))',
              },
            }}
          />
        </ThemeProvider>
      </body>
    </html>
  );
}
