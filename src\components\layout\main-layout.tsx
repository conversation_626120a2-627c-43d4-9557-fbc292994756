'use client'

import * as React from 'react'
import { Header } from './header'
import { Footer } from './footer'
import { MobileNavigation } from './navigation'
import { cn } from '@/utils'

interface MainLayoutProps {
  children: React.ReactNode
  className?: string
  showMobileNav?: boolean
}

export function MainLayout({ 
  children, 
  className,
  showMobileNav = true 
}: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className={cn('flex-1 pt-16', className)}>
        {children}
      </main>
      
      <Footer />
      
      {showMobileNav && <MobileNavigation />}
      
      {/* Add padding bottom for mobile navigation */}
      {showMobileNav && <div className="h-16 md:hidden" />}
    </div>
  )
}
