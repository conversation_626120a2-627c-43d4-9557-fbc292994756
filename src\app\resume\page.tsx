'use client'

import { motion } from 'framer-motion'
import { Download, Printer, Share2 } from 'lucide-react'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ExperienceSection } from '@/components/portfolio/experience-section'
import { EducationSection } from '@/components/portfolio/education-section'

export default function ResumePage() {
  const handleDownload = () => {
    // This would trigger a PDF download
    console.log('Download resume PDF')
  }

  const handlePrint = () => {
    window.print()
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: '<PERSON><PERSON>badeyi - Resume',
        text: 'Check out my professional resume',
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
    }
  }

  return (
    <MainLayout>
      <div className="min-h-screen py-12">
        <div className="container-custom max-w-4xl">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              My <span className="gradient-text">Resume</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              A comprehensive overview of my professional journey and achievements
            </p>
            
            {/* Action Buttons */}
            <div className="flex flex-wrap justify-center gap-4">
              <Button onClick={handleDownload} size="lg">
                <Download className="h-5 w-5 mr-2" />
                Download PDF
              </Button>
              <Button variant="outline" onClick={handlePrint} size="lg">
                <Printer className="h-5 w-5 mr-2" />
                Print
              </Button>
              <Button variant="outline" onClick={handleShare} size="lg">
                <Share2 className="h-5 w-5 mr-2" />
                Share
              </Button>
            </div>
          </motion.div>

          {/* Resume Content */}
          <div className="space-y-12">
            {/* Professional Summary */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">Professional Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    Experienced Full Stack Developer with 5+ years of expertise in building scalable 
                    web applications and leading technical teams. Passionate about creating exceptional 
                    digital experiences with modern technologies including React, Node.js, TypeScript, 
                    and cloud platforms. Proven track record of delivering high-quality solutions for 
                    clients across multiple continents and mentoring the next generation of developers.
                  </p>
                </CardContent>
              </Card>
            </motion.section>

            {/* Work Experience */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <h2 className="text-3xl font-bold mb-6">Work Experience</h2>
              <ExperienceSection showAll={true} />
            </motion.section>

            {/* Education & Certifications */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <EducationSection showCertifications={true} />
            </motion.section>

            {/* Volunteer Experience */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">Volunteer & Community Experience</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-lg">Co-Lead, Technicals</h4>
                      <p className="text-primary font-medium">Google Developer Group (GDG) Ibadan | November 2024 - June 2025</p>
                      <ul className="mt-2 space-y-1 text-muted-foreground">
                        <li>• Served as technical co-lead for GDG Ibadan for DevFest Ibadan and Google-backed events with 3,000+ attendees</li>
                        <li>• Led the planning and delivery of hands-on technical sessions, managed volunteer developer teams</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-lg">Mathematics Teacher – NYSC</h4>
                      <p className="text-primary font-medium">Pathfinder College | November 2023 - October 2024</p>
                      <ul className="mt-2 space-y-1 text-muted-foreground">
                        <li>• Taught junior and senior secondary school mathematics during national service</li>
                        <li>• Used clear, structured teaching approaches that later supported mentoring junior developers</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.section>

            {/* Key Projects */}
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.0 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">Key Projects</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      'Smooth Beauty (User, Vendor, Admin & Delivery)',
                      'Crowned Naturally',
                      'Shoppoint (User & Provider Applications)',
                      'Unwind (User & Provider Applications)',
                      'Azura (Online marketplace)'
                    ].map((project, index) => (
                      <div key={project} className="p-4 bg-muted rounded-lg">
                        <h4 className="font-medium">{project}</h4>
                        <p className="text-sm text-muted-foreground mt-1">
                          Full-stack application with modern technologies
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.section>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
