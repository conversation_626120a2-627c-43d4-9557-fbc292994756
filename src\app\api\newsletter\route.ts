import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import connectDB from '@/lib/mongodb'
import { Newsletter } from '@/lib/models'
import { sendNewsletterWelcome } from '@/lib/email'
import { ApiResponse } from '@/types'
import { getZodErrorMessage } from '@/utils'

// Validation schema
const newsletterSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const { email } = newsletterSchema.parse(body)
    
    // Connect to database
    await connectDB()
    
    // Check if email already exists
    const existingSubscription = await Newsletter.findOne({ email })
    
    if (existingSubscription) {
      if (existingSubscription.status === 'active') {
        const response: ApiResponse = {
          success: false,
          error: 'This email is already subscribed to our newsletter.',
        }
        return NextResponse.json(response, { status: 400 })
      } else {
        // Reactivate subscription
        existingSubscription.status = 'active'
        existingSubscription.subscribedAt = new Date()
        existingSubscription.unsubscribedAt = undefined
        await existingSubscription.save()
      }
    } else {
      // Create new subscription
      const subscription = new Newsletter({ email })
      await subscription.save()
    }
    
    // Send welcome email
    try {
      await sendNewsletterWelcome(email)
    } catch (emailError) {
      console.error('Error sending welcome email:', emailError)
      // Don't fail the request if email fails
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'Successfully subscribed to newsletter! Check your email for confirmation.',
    }
    
    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Newsletter subscription error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Something went wrong. Please try again later.',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    
    await connectDB()
    
    // Build query
    const query: any = {}
    if (status && ['active', 'unsubscribed'].includes(status)) {
      query.status = status
    }
    
    // Get subscriptions with pagination
    const skip = (page - 1) * limit
    const subscriptions = await Newsletter.find(query)
      .sort({ subscribedAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()
    
    const total = await Newsletter.countDocuments(query)
    
    const response: ApiResponse = {
      success: true,
      data: {
        subscriptions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Get newsletter subscriptions error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch newsletter subscriptions',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
