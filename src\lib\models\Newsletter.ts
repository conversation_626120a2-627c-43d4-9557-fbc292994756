import mongoose, { Schema } from 'mongoose'
import { INewsletter } from '@/types'

const NewsletterSchema = new Schema<INewsletter>(
  {
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
    },
    status: {
      type: String,
      enum: ['active', 'unsubscribed'],
      default: 'active',
    },
    subscribedAt: {
      type: Date,
      default: Date.now,
    },
    unsubscribedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
)

// Set unsubscribedAt when status changes to unsubscribed
NewsletterSchema.pre('save', function (next) {
  if (this.isModified('status') && this.status === 'unsubscribed' && !this.unsubscribedAt) {
    this.unsubscribedAt = new Date()
  }
  next()
})

// Indexes
NewsletterSchema.index({ email: 1 })
NewsletterSchema.index({ status: 1, subscribedAt: -1 })

export default mongoose.models.Newsletter || mongoose.model<INewsletter>('Newsletter', NewsletterSchema)
