/**
 * Debug Admin Login Script
 * 
 * This script helps debug admin login issues by checking the database
 * and testing password comparison.
 */

const { MongoClient } = require('mongodb')
const bcrypt = require('bcryptjs')
require('dotenv').config({ path: '.env.local' })

async function debugAdminLogin() {
  const mongoUri = process.env.MONGODB_URI
  const testEmail = '<EMAIL>'
  const testPassword = 'Olareborn@1'
  
  if (!mongoUri) {
    console.error('❌ MONGODB_URI not found in environment variables')
    process.exit(1)
  }

  let client

  try {
    console.log('🔍 Debugging admin login...\n')
    
    // Connect to MongoDB
    client = new MongoClient(mongoUri)
    await client.connect()
    
    const db = client.db()
    const users = db.collection('users')
    
    // Find the admin user
    console.log(`🔎 Looking for user with email: ${testEmail}`)
    const adminUser = await users.findOne({ email: testEmail })
    
    if (!adminUser) {
      console.log('❌ No user found with that email')
      console.log('\n📋 All users in database:')
      const allUsers = await users.find({}).toArray()
      allUsers.forEach(user => {
        console.log(`  - Email: ${user.email}, Role: ${user.role}, Name: ${user.name}`)
      })
      return
    }
    
    console.log('✅ User found!')
    console.log(`📧 Email: ${adminUser.email}`)
    console.log(`👤 Name: ${adminUser.name}`)
    console.log(`🔑 Role: ${adminUser.role}`)
    console.log(`📅 Created: ${adminUser.createdAt}`)
    console.log(`🔒 Password Hash: ${adminUser.password.substring(0, 20)}...`)
    
    // Test password comparison
    console.log(`\n🧪 Testing password comparison...`)
    console.log(`Password to test: "${testPassword}"`)
    
    try {
      const isValid = await bcrypt.compare(testPassword, adminUser.password)
      console.log(`✅ Password comparison result: ${isValid}`)
      
      if (!isValid) {
        console.log('\n❌ Password does not match!')
        console.log('💡 Possible issues:')
        console.log('  1. Password was not hashed correctly when saved')
        console.log('  2. Password was changed after creation')
        console.log('  3. There are extra spaces or characters')
        
        // Test with some variations
        const variations = [
          testPassword.trim(),
          testPassword.toLowerCase(),
          testPassword.toUpperCase(),
        ]
        
        console.log('\n🔄 Testing password variations:')
        for (const variation of variations) {
          const result = await bcrypt.compare(variation, adminUser.password)
          console.log(`  "${variation}": ${result}`)
        }
      } else {
        console.log('\n✅ Password matches! The issue might be elsewhere.')
      }
    } catch (error) {
      console.error('❌ Error during password comparison:', error.message)
    }
    
    // Check if password is already hashed
    console.log('\n🔍 Analyzing password hash...')
    if (adminUser.password.startsWith('$2a$') || adminUser.password.startsWith('$2b$')) {
      console.log('✅ Password appears to be properly hashed with bcrypt')
    } else {
      console.log('❌ Password does not appear to be hashed!')
      console.log('💡 This could be the issue - password should be hashed')
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// Run the debug
debugAdminLogin().catch(console.error)
