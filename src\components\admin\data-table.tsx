/**
 * Admin Data Table Component
 * 
 * Reusable data table with sorting, filtering, pagination,
 * and bulk actions for admin CRUD operations.
 */

'use client'

import { useState, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter,
  MoreHorizontal,
  Loader2,
  AlertCircle,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox'
import type { DataTableProps, DataTableColumn } from '@/types/admin'

/**
 * Generic Data Table Component
 */
export function DataTable<T extends Record<string, unknown>>({
  data,
  columns,
  loading = false,
  error = null,
  pagination,
  onPageChange,
  onSort,
  onFilter,
  onRowClick,
  onRowSelect,
  actions = [],
}: DataTableProps<T>): JSX.Element {
  const [selectedRows, setSelectedRows] = useState<T[]>([])
  const [sortColumn, setSortColumn] = useState<keyof T | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<Record<string, unknown>>({})

  /**
   * Handles column sorting
   */
  const handleSort = (column: keyof T) => {
    if (!columns.find(col => col.key === column)?.sortable) return

    const newDirection = sortColumn === column && sortDirection === 'asc' ? 'desc' : 'asc'
    setSortColumn(column)
    setSortDirection(newDirection)
    onSort?.(column, newDirection)
  }

  /**
   * Handles row selection
   */
  const handleRowSelect = (row: T, checked: boolean) => {
    const newSelectedRows = checked
      ? [...selectedRows, row]
      : selectedRows.filter(selectedRow => selectedRow !== row)
    
    setSelectedRows(newSelectedRows)
    onRowSelect?.(newSelectedRows)
  }

  /**
   * Handles select all rows
   */
  const handleSelectAll = (checked: boolean) => {
    const newSelectedRows = checked ? [...data] : []
    setSelectedRows(newSelectedRows)
    onRowSelect?.(newSelectedRows)
  }

  /**
   * Filters data based on search query
   */
  const filteredData = useMemo(() => {
    if (!searchQuery) return data

    return data.filter(row =>
      columns.some(column => {
        const value = row[column.key]
        return String(value).toLowerCase().includes(searchQuery.toLowerCase())
      })
    )
  }, [data, searchQuery, columns])

  /**
   * Renders table cell content
   */
  const renderCell = (row: T, column: DataTableColumn<T>) => {
    const value = row[column.key]
    
    if (column.render) {
      return column.render(value, row)
    }

    if (value === null || value === undefined) {
      return <span className="text-muted-foreground">—</span>
    }

    if (typeof value === 'boolean') {
      return (
        <span className={`px-2 py-1 rounded-full text-xs ${
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value ? 'Yes' : 'No'}
        </span>
      )
    }

    if (value instanceof Date) {
      return value.toLocaleDateString()
    }

    return String(value)
  }

  /**
   * Renders pagination controls
   */
  const renderPagination = () => {
    if (!pagination) return null

    const { page, pages, total } = pagination
    const startItem = (page - 1) * pagination.limit + 1
    const endItem = Math.min(page * pagination.limit, total)

    return (
      <div className="flex items-center justify-between px-6 py-4 border-t">
        <div className="text-sm text-muted-foreground">
          Showing {startItem} to {endItem} of {total} results
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(page - 1)}
            disabled={page <= 1}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, pages) }, (_, i) => {
              const pageNum = i + 1
              return (
                <Button
                  key={pageNum}
                  variant={page === pageNum ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => onPageChange?.(pageNum)}
                >
                  {pageNum}
                </Button>
              )
            })}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(page + 1)}
            disabled={page >= pages}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <Card>
      {/* Table Header with Search and Filters */}
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Data Table</CardTitle>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64 pl-8"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedRows.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="flex items-center justify-between p-3 bg-muted rounded-lg"
          >
            <span className="text-sm font-medium">
              {selectedRows.length} row{selectedRows.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                Export
              </Button>
              <Button variant="destructive" size="sm">
                Delete
              </Button>
            </div>
          </motion.div>
        )}
      </CardHeader>

      <CardContent className="p-0">
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex items-center justify-center py-12 text-destructive">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>{error}</span>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredData.length === 0 && (
          <div className="flex items-center justify-center py-12 text-muted-foreground">
            <span>No data available</span>
          </div>
        )}

        {/* Table */}
        {!loading && !error && filteredData.length > 0 && (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b">
                <tr>
                  {/* Select All Checkbox */}
                  <th className="w-12 px-6 py-3 text-left">
                    <Checkbox
                      checked={selectedRows.length === filteredData.length}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all rows"
                    />
                  </th>
                  
                  {/* Column Headers */}
                  {columns.map((column) => (
                    <th
                      key={String(column.key)}
                      className={`px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider ${
                        column.sortable ? 'cursor-pointer hover:text-foreground' : ''
                      }`}
                      style={{ width: column.width }}
                      onClick={() => column.sortable && handleSort(column.key)}
                    >
                      <div className="flex items-center space-x-1">
                        <span>{column.label}</span>
                        {column.sortable && (
                          <div className="flex flex-col">
                            <ChevronUp
                              className={`h-3 w-3 ${
                                sortColumn === column.key && sortDirection === 'asc'
                                  ? 'text-foreground'
                                  : 'text-muted-foreground'
                              }`}
                            />
                            <ChevronDown
                              className={`h-3 w-3 -mt-1 ${
                                sortColumn === column.key && sortDirection === 'desc'
                                  ? 'text-foreground'
                                  : 'text-muted-foreground'
                              }`}
                            />
                          </div>
                        )}
                      </div>
                    </th>
                  ))}
                  
                  {/* Actions Column */}
                  {actions.length > 0 && (
                    <th className="w-16 px-6 py-3 text-right">
                      <span className="sr-only">Actions</span>
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                <AnimatePresence>
                  {filteredData.map((row, index) => (
                    <motion.tr
                      key={index}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className={`hover:bg-muted/50 transition-colors ${
                        onRowClick ? 'cursor-pointer' : ''
                      }`}
                      onClick={() => onRowClick?.(row)}
                    >
                      {/* Row Selection Checkbox */}
                      <td className="px-6 py-4">
                        <Checkbox
                          checked={selectedRows.includes(row)}
                          onCheckedChange={(checked) => handleRowSelect(row, checked as boolean)}
                          onClick={(e) => e.stopPropagation()}
                          aria-label={`Select row ${index + 1}`}
                        />
                      </td>
                      
                      {/* Data Cells */}
                      {columns.map((column) => (
                        <td key={String(column.key)} className="px-6 py-4 text-sm">
                          {renderCell(row, column)}
                        </td>
                      ))}
                      
                      {/* Actions Dropdown */}
                      {actions.length > 0 && (
                        <td className="px-6 py-4 text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {actions.map((action, actionIndex) => (
                                <DropdownMenuItem
                                  key={actionIndex}
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    action.onClick(row)
                                  }}
                                  disabled={action.disabled?.(row)}
                                >
                                  {action.icon && <action.icon className="h-4 w-4 mr-2" />}
                                  {action.label}
                                </DropdownMenuItem>
                              ))}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      )}
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {renderPagination()}
      </CardContent>
    </Card>
  )
}
