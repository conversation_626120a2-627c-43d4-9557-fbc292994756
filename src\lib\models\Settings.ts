import mongoose, { Schema } from 'mongoose'
import { ISettings } from '@/types'

const SettingsSchema = new Schema<ISettings>(
  {
    key: {
      type: String,
      required: [true, 'Key is required'],
      unique: true,
      trim: true,
      maxlength: [100, 'Key cannot exceed 100 characters'],
    },
    value: {
      type: Schema.Types.Mixed,
      required: [true, 'Value is required'],
    },
    type: {
      type: String,
      enum: ['string', 'number', 'boolean', 'object', 'array'],
      required: [true, 'Type is required'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },
  },
  {
    timestamps: true,
  }
)

// Index for quick lookups
SettingsSchema.index({ key: 1 })

export default mongoose.models.Settings || mongoose.model<ISettings>('Settings', SettingsSchema)
