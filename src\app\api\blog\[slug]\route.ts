import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import connectDB from '@/lib/mongodb'
import { BlogPost } from '@/lib/models'
import { requireAdmin } from '@/lib/auth'
import { ApiResponse } from '@/types'
import { generateSlug, getZodErrorMessage } from '@/utils'

// Validation schema
const blogPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title cannot exceed 200 characters'),
  slug: z.string().optional(),
  excerpt: z.string().min(1, 'Excerpt is required').max(300, 'Excerpt cannot exceed 300 characters'),
  content: z.string().min(1, 'Content is required'),
  featuredImage: z.string().optional(),
  tags: z.array(z.string()).default([]),
  category: z.string().min(1, 'Category is required').max(50, 'Category cannot exceed 50 characters'),
  published: z.boolean().default(false),
  seoTitle: z.string().max(60, 'SEO title cannot exceed 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description cannot exceed 160 characters').optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB()
    
    // Build query - only show published posts for public requests
    const authHeader = request.headers.get('authorization')
    const query: any = { slug: params.slug }
    
    if (!authHeader) {
      query.published = true
    }
    
    const post = await BlogPost.findOne(query).lean()

    if (!post) {
      const response: ApiResponse = {
        success: false,
        error: 'Blog post not found',
      }
      return NextResponse.json(response, { status: 404 })
    }

    // Increment views for published posts
    if ((post as any).published && !authHeader) {
      await BlogPost.findByIdAndUpdate((post as any)._id, { $inc: { views: 1 } })
    }
    
    const response: ApiResponse = {
      success: true,
      data: post,
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Get blog post error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch blog post',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check admin authentication
    const authResult = await requireAdmin(request)
    if ('error' in authResult) {
      const response: ApiResponse = {
        success: false,
        error: authResult.error,
      }
      return NextResponse.json(response, { status: authResult.status })
    }
    
    const body = await request.json()
    
    // Validate input
    const validatedData = blogPostSchema.parse(body)
    
    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.title)
    }
    
    // Connect to database
    await connectDB()
    
    // Check if new slug conflicts with existing posts
    if (validatedData.slug !== params.slug) {
      const existingPost = await BlogPost.findOne({ slug: validatedData.slug })
      if (existingPost) {
        const response: ApiResponse = {
          success: false,
          error: 'A post with this slug already exists',
        }
        return NextResponse.json(response, { status: 400 })
      }
    }
    
    // Update blog post
    const post = await BlogPost.findOneAndUpdate(
      { slug: params.slug },
      validatedData,
      { new: true, runValidators: true }
    )
    
    if (!post) {
      const response: ApiResponse = {
        success: false,
        error: 'Blog post not found',
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      data: post,
      message: 'Blog post updated successfully',
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Update blog post error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to update blog post',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check admin authentication
    const authResult = await requireAdmin(request)
    if ('error' in authResult) {
      const response: ApiResponse = {
        success: false,
        error: authResult.error,
      }
      return NextResponse.json(response, { status: authResult.status })
    }
    
    // Connect to database
    await connectDB()
    
    // Delete blog post
    const post = await BlogPost.findOneAndDelete({ slug: params.slug })
    
    if (!post) {
      const response: ApiResponse = {
        success: false,
        error: 'Blog post not found',
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'Blog post deleted successfully',
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Delete blog post error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to delete blog post',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
