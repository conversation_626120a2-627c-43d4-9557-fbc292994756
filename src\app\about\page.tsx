'use client'

import { motion } from 'framer-motion'
import { Download, MapPin, Mail, Phone, Calendar, ExternalLink } from 'lucide-react'
import Link from 'next/link'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const personalInfo = {
  name: '<PERSON><PERSON> Obadeyi',
  title: 'Software Engineer',
  location: 'UTC+1',
  email: '<EMAIL>',
  phone: '+234 (0) ************',
  linkedin: 'linkedin.com/in/seyiobadeyi',
  github: 'github.com/seyiobadeyi',
  website: 'seyi.obadeyi.com',
}

const summary = `Experienced Software Engineer with 5+ years of expertise in building scalable web applications and leading technical teams. Passionate about creating exceptional digital experiences with modern technologies including React, Node.js, TypeScript, and cloud platforms. Proven track record of delivering high-quality solutions for clients across multiple continents and mentoring the next generation of developers.`

const skills = {
  'Backend Development': [
    'Node.js', 'Express.js', 'FastAPI', 'PHP', 'Laravel', 'Python', 'MongoDB', 
    'MySQL', 'PostgreSQL', '.NET Core', 'C#', 'GraphQL', 'REST APIs'
  ],
  'Frontend Development': [
    'React.js', 'Next.js', 'Vue.js', 'TypeScript', 'JavaScript', 'HTML5', 
    'CSS3', 'Tailwind CSS', 'Redux', 'Three.js', 'Chart.js'
  ],
  'DevOps & Cloud': [
    'Docker', 'Kubernetes', 'AWS', 'GCP', 'Jenkins', 'GitHub Actions', 
    'Terraform', 'Monitoring', 'CI/CD'
  ],
  'Tools & Testing': [
    'Git', 'Jest', 'Cypress', 'Playwright', 'Figma', 'Postman', 
    'Jira', 'Agile/Scrum'
  ]
}

export default function AboutPage() {
  return (
    <MainLayout>
      <div className="min-h-screen py-12">
        <div className="container-custom">
          {/* Header Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              About <span className="gradient-text">Me</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Get to know more about my journey, skills, and passion for creating 
              exceptional digital experiences.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Personal Info Card */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="lg:col-span-1"
            >
              <Card className="sticky top-24">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-xl">
                      S
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">{personalInfo.name}</h3>
                      <p className="text-muted-foreground">{personalInfo.title}</p>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 text-sm">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{personalInfo.location}</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <Link 
                        href={`mailto:${personalInfo.email}`}
                        className="hover:text-primary transition-colors"
                      >
                        {personalInfo.email}
                      </Link>
                    </div>
                    <div className="flex items-center gap-3 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{personalInfo.phone}</span>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="space-y-2">
                      <Link 
                        href={`https://${personalInfo.linkedin}`}
                        target="_blank"
                        className="flex items-center gap-2 text-sm hover:text-primary transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                        LinkedIn
                      </Link>
                      <Link 
                        href={`https://${personalInfo.github}`}
                        target="_blank"
                        className="flex items-center gap-2 text-sm hover:text-primary transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                        GitHub
                      </Link>
                      <Link 
                        href={`https://${personalInfo.website}`}
                        target="_blank"
                        className="flex items-center gap-2 text-sm hover:text-primary transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                        Website
                      </Link>
                    </div>
                  </div>

                  <Button asChild className="w-full mt-6">
                    <Link href="/resume">
                      <Download className="h-4 w-4 mr-2" />
                      Download Resume
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Main Content */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="lg:col-span-2 space-y-8"
            >
              {/* Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Professional Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {summary}
                  </p>
                </CardContent>
              </Card>

              {/* Skills */}
              <Card>
                <CardHeader>
                  <CardTitle>Technical Skills</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {Object.entries(skills).map(([category, skillList], index) => (
                      <motion.div
                        key={category}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                      >
                        <h4 className="font-semibold mb-3 text-primary">{category}</h4>
                        <div className="flex flex-wrap gap-2">
                          {skillList.map((skill) => (
                            <span
                              key={skill}
                              className="px-3 py-1 bg-muted text-muted-foreground rounded-full text-sm hover:bg-primary hover:text-primary-foreground transition-colors cursor-default"
                            >
                              {skill}
                            </span>
                          ))}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  { label: 'Years Experience', value: '5+' },
                  { label: 'Projects Completed', value: '50+' },
                  { label: 'Students Trained', value: '200+' },
                  { label: 'Countries Served', value: '10+' },
                ].map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                  >
                    <Card className="text-center">
                      <CardContent className="p-6">
                        <div className="text-2xl font-bold text-primary mb-1">
                          {stat.value}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {stat.label}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
