import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { NextRequest } from 'next/server'
import { User } from './models'
import connectDB from './mongodb'

const JWT_SECRET = process.env.JWT_SECRET!

if (!JWT_SECRET) {
  throw new Error('Please define the JWT_SECRET environment variable inside .env.local')
}

export interface JWTPayload {
  userId: string
  email: string
  role: string
  iat?: number
  exp?: number
}

// Generate JWT token
export function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '7d', // Token expires in 7 days
  })
}

// Verify JWT token
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload
  } catch (error) {
    return null
  }
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(12)
  return bcrypt.hash(password, salt)
}

// Compare password
export async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// Get user from token
export async function getUserFromToken(token: string) {
  try {
    const payload = verifyToken(token)
    if (!payload) return null

    await connectDB()
    const user = await User.findById(payload.userId).select('-password')
    return user
  } catch (error) {
    return null
  }
}

// Extract token from request
export function getTokenFromRequest(request: NextRequest): string | null {
  // Check Authorization header
  const authHeader = request.headers.get('authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // Check cookies
  const token = request.cookies.get('auth-token')?.value
  return token || null
}

// Middleware to check authentication
export async function requireAuth(request: NextRequest) {
  const token = getTokenFromRequest(request)
  
  if (!token) {
    return { error: 'No token provided', status: 401 }
  }

  const user = await getUserFromToken(token)
  
  if (!user) {
    return { error: 'Invalid token', status: 401 }
  }

  return { user }
}

// Middleware to check admin role
export async function requireAdmin(request: NextRequest) {
  const authResult = await requireAuth(request)
  
  if ('error' in authResult) {
    return authResult
  }

  if (authResult.user.role !== 'admin') {
    return { error: 'Admin access required', status: 403 }
  }

  return authResult
}

// Create default admin user
export async function createDefaultAdmin() {
  try {
    await connectDB()
    
    const adminEmail = process.env.ADMIN_EMAIL!
    const adminPassword = process.env.ADMIN_PASSWORD!
    
    if (!adminEmail || !adminPassword) {
      console.warn('ADMIN_EMAIL and ADMIN_PASSWORD must be set in environment variables')
      return
    }

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: adminEmail })
    if (existingAdmin) {
      console.log('Admin user already exists')
      return
    }

    // Create admin user
    const admin = new User({
      email: adminEmail,
      password: adminPassword,
      name: 'Seyi Obadeyi',
      role: 'admin',
    })

    await admin.save()
    console.log('Default admin user created successfully')
  } catch (error) {
    console.error('Error creating default admin user:', error)
  }
}
