import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import connectDB from '@/lib/mongodb'
import { Newsletter } from '@/lib/models'
import { ApiResponse } from '@/types'
import { getZodErrorMessage } from '@/utils'

// Validation schema
const unsubscribeSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const { email } = unsubscribeSchema.parse(body)
    
    // Connect to database
    await connectDB()
    
    // Find and update subscription
    const subscription = await Newsletter.findOne({ email })
    
    if (!subscription) {
      const response: ApiResponse = {
        success: false,
        error: 'Email not found in our newsletter list.',
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    if (subscription.status === 'unsubscribed') {
      const response: ApiResponse = {
        success: false,
        error: 'This email is already unsubscribed.',
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // Unsubscribe
    subscription.status = 'unsubscribed'
    subscription.unsubscribedAt = new Date()
    await subscription.save()
    
    const response: ApiResponse = {
      success: true,
      message: 'Successfully unsubscribed from newsletter.',
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Newsletter unsubscribe error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Something went wrong. Please try again later.',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    
    if (!email) {
      return NextResponse.redirect(new URL('/', request.url))
    }
    
    // Validate email
    const { email: validEmail } = unsubscribeSchema.parse({ email })
    
    // Connect to database
    await connectDB()
    
    // Find subscription
    const subscription = await Newsletter.findOne({ email: validEmail })
    
    if (!subscription) {
      return NextResponse.redirect(new URL('/?error=email-not-found', request.url))
    }
    
    if (subscription.status === 'unsubscribed') {
      return NextResponse.redirect(new URL('/?message=already-unsubscribed', request.url))
    }
    
    // Unsubscribe
    subscription.status = 'unsubscribed'
    subscription.unsubscribedAt = new Date()
    await subscription.save()
    
    return NextResponse.redirect(new URL('/?message=unsubscribed', request.url))
  } catch (error) {
    console.error('Newsletter unsubscribe error:', error)
    return NextResponse.redirect(new URL('/?error=unsubscribe-failed', request.url))
  }
}
