/**
 * Admin Authentication System
 * 
 * Provides secure authentication and authorization for admin users
 * with proper session management, role-based access control, and security measures.
 */

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from './nextauth'
import { User } from './models'
import connectDB from './mongodb'
import type { AdminSession, AdminApiResponse, AdminRole, AdminPermission } from '@/types/admin'

/**
 * Security configuration constants
 */
const SECURITY_CONFIG = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  CSRF_TOKEN_LENGTH: 32,
} as const

/**
 * Admin role definitions with granular permissions
 */
const ADMIN_ROLES: Record<string, AdminRole> = {
  super_admin: {
    id: 'super_admin',
    name: 'Super Administrator',
    description: 'Full system access with all permissions',
    permissions: [
      { resource: 'projects', actions: ['create', 'read', 'update', 'delete', 'publish', 'archive'] },
      { resource: 'blog-posts', actions: ['create', 'read', 'update', 'delete', 'publish', 'archive'] },
      { resource: 'contacts', actions: ['read', 'update', 'delete', 'archive'] },
      { resource: 'newsletter', actions: ['read', 'update', 'delete'] },
      { resource: 'users', actions: ['create', 'read', 'update', 'delete'] },
      { resource: 'settings', actions: ['read', 'update'] },
    ],
  },
  admin: {
    id: 'admin',
    name: 'Administrator',
    description: 'Standard admin access for content management',
    permissions: [
      { resource: 'projects', actions: ['create', 'read', 'update', 'delete', 'publish'] },
      { resource: 'blog-posts', actions: ['create', 'read', 'update', 'delete', 'publish'] },
      { resource: 'contacts', actions: ['read', 'update'] },
      { resource: 'newsletter', actions: ['read'] },
      { resource: 'settings', actions: ['read'] },
    ],
  },
} as const

/**
 * Rate limiting store for login attempts
 */
const loginAttempts = new Map<string, { count: number; lastAttempt: number; lockedUntil?: number }>()

/**
 * Validates admin session and returns user information
 * 
 * @param request - Next.js request object
 * @returns Promise resolving to admin session or null
 */
export async function getAdminSession(request?: NextRequest): Promise<AdminSession | null> {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'admin') {
      return null
    }

    // Validate session hasn't expired
    const sessionExpiry = new Date(session.expires).getTime()
    const now = Date.now()
    
    if (now > sessionExpiry) {
      return null
    }

    return {
      user: {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: 'admin',
      },
      expires: session.expires,
    }
  } catch (error) {
    console.error('Error validating admin session:', error)
    return null
  }
}

/**
 * Middleware to require admin authentication
 * 
 * @param request - Next.js request object
 * @returns Promise resolving to success response or error
 */
export async function requireAdminAuth(request: NextRequest): Promise<
  | { success: true; session: AdminSession }
  | { success: false; error: string; status: number }
> {
  const session = await getAdminSession(request)
  
  if (!session) {
    return {
      success: false,
      error: 'Authentication required. Please log in as an administrator.',
      status: 401,
    }
  }

  return { success: true, session }
}

/**
 * Checks if admin user has specific permission
 * 
 * @param session - Admin session object
 * @param resource - Resource to check permission for
 * @param action - Action to check permission for
 * @returns Boolean indicating if permission is granted
 */
export function hasAdminPermission(
  session: AdminSession,
  resource: AdminPermission['resource'],
  action: AdminPermission['actions'][number]
): boolean {
  // For now, all admin users have full permissions
  // This can be extended to support role-based permissions
  return session.user.role === 'admin'
}

/**
 * Rate limiting for login attempts
 * 
 * @param identifier - IP address or user identifier
 * @returns Object indicating if request is allowed and remaining attempts
 */
export function checkRateLimit(identifier: string): {
  allowed: boolean
  remainingAttempts: number
  lockedUntil?: number
} {
  const now = Date.now()
  const attempts = loginAttempts.get(identifier)

  if (!attempts) {
    return { allowed: true, remainingAttempts: SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS }
  }

  // Check if lockout period has expired
  if (attempts.lockedUntil && now > attempts.lockedUntil) {
    loginAttempts.delete(identifier)
    return { allowed: true, remainingAttempts: SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS }
  }

  // Check if still locked out
  if (attempts.lockedUntil && now <= attempts.lockedUntil) {
    return {
      allowed: false,
      remainingAttempts: 0,
      lockedUntil: attempts.lockedUntil,
    }
  }

  // Check if max attempts exceeded
  if (attempts.count >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
    const lockedUntil = now + SECURITY_CONFIG.LOCKOUT_DURATION
    loginAttempts.set(identifier, { ...attempts, lockedUntil })
    return {
      allowed: false,
      remainingAttempts: 0,
      lockedUntil,
    }
  }

  return {
    allowed: true,
    remainingAttempts: SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS - attempts.count,
  }
}

/**
 * Records a failed login attempt
 * 
 * @param identifier - IP address or user identifier
 */
export function recordFailedAttempt(identifier: string): void {
  const now = Date.now()
  const attempts = loginAttempts.get(identifier)

  if (!attempts) {
    loginAttempts.set(identifier, { count: 1, lastAttempt: now })
  } else {
    loginAttempts.set(identifier, {
      count: attempts.count + 1,
      lastAttempt: now,
      lockedUntil: attempts.lockedUntil,
    })
  }
}

/**
 * Clears failed login attempts for successful login
 * 
 * @param identifier - IP address or user identifier
 */
export function clearFailedAttempts(identifier: string): void {
  loginAttempts.delete(identifier)
}

/**
 * Validates admin user credentials
 * 
 * @param email - User email
 * @param password - User password
 * @returns Promise resolving to validation result
 */
export async function validateAdminCredentials(
  email: string,
  password: string
): Promise<{ valid: boolean; user?: AdminSession['user']; error?: string }> {
  try {
    await connectDB()
    
    const user = await User.findOne({ email: email.toLowerCase() })
    
    if (!user || user.role !== 'admin') {
      return { valid: false, error: 'Invalid credentials or insufficient permissions' }
    }

    const isValidPassword = await user.comparePassword(password)
    
    if (!isValidPassword) {
      return { valid: false, error: 'Invalid credentials' }
    }

    return {
      valid: true,
      user: {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        role: 'admin',
      },
    }
  } catch (error) {
    console.error('Error validating admin credentials:', error)
    return { valid: false, error: 'Authentication service unavailable' }
  }
}

/**
 * Creates standardized admin API response
 * 
 * @param data - Response data
 * @param message - Success message
 * @param meta - Additional metadata
 * @returns Standardized API response
 */
export function createAdminResponse<T>(
  data?: T,
  message?: string,
  meta?: AdminApiResponse<T>['meta']
): AdminApiResponse<T> {
  return {
    success: true,
    data,
    message,
    meta,
  }
}

/**
 * Creates standardized admin error response
 * 
 * @param error - Error message
 * @param status - HTTP status code
 * @returns Next.js response with error
 */
export function createAdminErrorResponse(error: string, status: number = 400): NextResponse {
  const response: AdminApiResponse = {
    success: false,
    error,
  }
  
  return NextResponse.json(response, { status })
}

/**
 * Logs admin actions for audit trail
 * 
 * @param session - Admin session
 * @param action - Action performed
 * @param resource - Resource affected
 * @param details - Additional details
 */
export async function logAdminAction(
  session: AdminSession,
  action: string,
  resource: string,
  details?: Record<string, unknown>
): Promise<void> {
  try {
    // In a production environment, this would write to an audit log
    console.log('Admin Action:', {
      userId: session.user.id,
      userEmail: session.user.email,
      action,
      resource,
      details,
      timestamp: new Date().toISOString(),
      ip: 'unknown', // Would be extracted from request in real implementation
    })
  } catch (error) {
    console.error('Error logging admin action:', error)
  }
}
