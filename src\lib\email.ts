import nodemailer from 'nodemailer'

// Email configuration
const emailConfig = {
  host: process.env.EMAIL_HOST!,
  port: parseInt(process.env.EMAIL_PORT!) || 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.EMAIL_USER!,
    pass: process.env.EMAIL_PASS!,
  },
}

// Create transporter
const transporter = nodemailer.createTransport(emailConfig)

// Verify email configuration
export async function verifyEmailConfig(): Promise<boolean> {
  try {
    await transporter.verify()
    console.log('Email configuration is valid')
    return true
  } catch (error) {
    console.error('Email configuration error:', error)
    return false
  }
}

// Send email interface
interface SendEmailOptions {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  from?: string
}

// Send email function
export async function sendEmail(options: SendEmailOptions): Promise<boolean> {
  try {
    const { to, subject, html, text, from } = options
    
    const mailOptions = {
      from: from || process.env.EMAIL_FROM!,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html,
      text,
    }

    const result = await transporter.sendMail(mailOptions)
    console.log('Email sent successfully:', result.messageId)
    return true
  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}

// Email templates
export const emailTemplates = {
  // Contact form notification
  contactNotification: (data: { name: string; email: string; subject: string; message: string }) => ({
    subject: `New Contact Form Submission: ${data.subject}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">New Contact Form Submission</h2>
        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> ${data.name}</p>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Subject:</strong> ${data.subject}</p>
          <p><strong>Message:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
            ${data.message.replace(/\n/g, '<br>')}
          </div>
        </div>
        <p style="color: #666; font-size: 14px;">
          This email was sent from your portfolio contact form.
        </p>
      </div>
    `,
    text: `
      New Contact Form Submission
      
      Name: ${data.name}
      Email: ${data.email}
      Subject: ${data.subject}
      
      Message:
      ${data.message}
    `,
  }),

  // Contact form auto-reply
  contactAutoReply: (name: string) => ({
    subject: 'Thank you for your message - Seyi Obadeyi',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Thank you for reaching out!</h2>
        <p>Hi ${name},</p>
        <p>Thank you for your message. I've received your inquiry and will get back to you as soon as possible, typically within 24-48 hours.</p>
        <p>In the meantime, feel free to:</p>
        <ul>
          <li>Check out my latest projects on <a href="${process.env.NEXT_PUBLIC_GITHUB_URL}">GitHub</a></li>
          <li>Connect with me on <a href="${process.env.NEXT_PUBLIC_LINKEDIN_URL}">LinkedIn</a></li>
          <li>Read my latest blog posts on my <a href="${process.env.NEXT_PUBLIC_APP_URL}/blog">blog</a></li>
        </ul>
        <p>Best regards,<br>Seyi Obadeyi</p>
        <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px; color: #666; font-size: 14px;">
          <p>Seyi Obadeyi - Software Engineer</p>
          <p>Email: ${process.env.EMAIL_FROM}</p>
          <p>Website: ${process.env.NEXT_PUBLIC_APP_URL}</p>
        </div>
      </div>
    `,
    text: `
      Hi ${name},
      
      Thank you for your message. I've received your inquiry and will get back to you as soon as possible, typically within 24-48 hours.
      
      Best regards,
      Seyi Obadeyi
      
      ---
      Seyi Obadeyi - Software Engineer
      Email: ${process.env.EMAIL_FROM}
      Website: ${process.env.NEXT_PUBLIC_APP_URL}
    `,
  }),

  // Newsletter welcome
  newsletterWelcome: (email: string) => ({
    subject: 'Welcome to my newsletter! - Seyi Obadeyi',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Welcome to my newsletter!</h2>
        <p>Hi there,</p>
        <p>Thank you for subscribing to my newsletter! You'll now receive updates about:</p>
        <ul>
          <li>New blog posts and technical articles</li>
          <li>Project updates and launches</li>
          <li>Industry insights and tips</li>
          <li>Exclusive content and resources</li>
        </ul>
        <p>I promise to keep the content valuable and not spam your inbox.</p>
        <p>Best regards,<br>Seyi Obadeyi</p>
        <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px; color: #666; font-size: 14px;">
          <p>You can unsubscribe at any time by clicking <a href="${process.env.NEXT_PUBLIC_APP_URL}/newsletter/unsubscribe?email=${encodeURIComponent(email)}">here</a>.</p>
        </div>
      </div>
    `,
    text: `
      Welcome to my newsletter!
      
      Hi there,
      
      Thank you for subscribing to my newsletter! You'll now receive updates about new blog posts, project updates, industry insights, and exclusive content.
      
      Best regards,
      Seyi Obadeyi
      
      ---
      You can unsubscribe at any time by visiting: ${process.env.NEXT_PUBLIC_APP_URL}/newsletter/unsubscribe?email=${encodeURIComponent(email)}
    `,
  }),
}

// Send contact form notification
export async function sendContactNotification(data: { name: string; email: string; subject: string; message: string }) {
  const template = emailTemplates.contactNotification(data)
  return sendEmail({
    to: process.env.EMAIL_FROM!,
    ...template,
  })
}

// Send contact form auto-reply
export async function sendContactAutoReply(to: string, name: string) {
  const template = emailTemplates.contactAutoReply(name)
  return sendEmail({
    to,
    ...template,
  })
}

// Send newsletter welcome email
export async function sendNewsletterWelcome(email: string) {
  const template = emailTemplates.newsletterWelcome(email)
  return sendEmail({
    to: email,
    ...template,
  })
}
