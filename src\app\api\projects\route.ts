import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/nextauth'
import connectDB from '@/lib/mongodb'
import { Project } from '@/lib/models'
import { ApiResponse } from '@/types'
import { getZodErrorMessage } from '@/utils'

// Validation schema
const projectSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title cannot exceed 100 characters'),
  description: z.string().min(1, 'Description is required').max(500, 'Description cannot exceed 500 characters'),
  longDescription: z.string().max(2000, 'Long description cannot exceed 2000 characters').optional(),
  technologies: z.array(z.string()).min(1, 'At least one technology is required'),
  category: z.enum(['web', 'mobile', 'wordpress', 'other']),
  featured: z.boolean().default(false),
  images: z.array(z.string()).default([]),
  liveUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  githubUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  demoUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  order: z.number().default(0),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const search = searchParams.get('search')

    await connectDB()

    // Build query
    const query: any = {}

    if (category && ['web', 'mobile', 'wordpress', 'other'].includes(category)) {
      query.category = category
    }

    if (featured === 'true') {
      query.featured = true
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { technologies: { $in: [new RegExp(search, 'i')] } },
      ]
    }

    // Get projects with pagination and error handling
    const skip = (page - 1) * limit
    const [projects, total] = await Promise.allSettled([
      Project.find(query)
        .sort({ order: 1, createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Project.countDocuments(query)
    ]).then(results => [
      results[0].status === 'fulfilled' ? results[0].value : [],
      results[1].status === 'fulfilled' ? results[1].value : 0
    ])

    // If no projects found, return sample data
    const finalProjects = projects.length > 0 ? projects : [
      {
        _id: '1',
        title: 'Smooth Beauty',
        description: 'Comprehensive beauty marketplace with user, vendor, admin, and delivery applications',
        technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'Socket.io', 'Stripe'],
        category: 'web',
        featured: true,
        liveUrl: 'https://smoothbeauty.com',
        githubUrl: 'https://github.com/seyiobadeyi/smooth-beauty',
        createdAt: new Date('2024-03-15')
      },
      {
        _id: '2',
        title: 'Crowned Naturally',
        description: 'E-commerce platform for natural hair care products',
        technologies: ['Next.js', 'TypeScript', 'Prisma', 'PostgreSQL', 'Tailwind CSS'],
        category: 'web',
        featured: true,
        liveUrl: 'https://crownednaturally.com',
        githubUrl: 'https://github.com/seyiobadeyi/crowned-naturally',
        createdAt: new Date('2024-02-20')
      }
    ]

    const finalTotal = total > 0 ? total : finalProjects.length

    const response: ApiResponse = {
      success: true,
      data: {
        projects: finalProjects,
        pagination: {
          page,
          limit,
          total: finalTotal,
          pages: Math.ceil(finalTotal / limit),
        },
      },
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Get projects error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch projects',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user?.role !== 'admin') {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication required. Admin access only.',
      }
      return NextResponse.json(response, { status: 401 })
    }
    
    const body = await request.json()
    
    // Validate input
    const validatedData = projectSchema.parse(body)
    
    // Connect to database
    await connectDB()
    
    // Create project
    const project = new Project(validatedData)
    await project.save()
    
    const response: ApiResponse = {
      success: true,
      data: project,
      message: 'Project created successfully',
    }
    
    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Create project error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create project',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
