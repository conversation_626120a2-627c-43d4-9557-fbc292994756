'use client'

import { useState, useEffect } from 'react'
import { ApiResponse } from '@/types'

interface UseApiOptions {
  immediate?: boolean
}

export function useApi<T>(
  url: string,
  options: RequestInit = {},
  { immediate = true }: UseApiOptions = {}
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(immediate)
  const [error, setError] = useState<string | null>(null)

  const execute = async (customOptions?: RequestInit) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(url, { ...options, ...customOptions })
      const result: ApiResponse<T> = await response.json()

      if (result.success) {
        setData(result.data || null)
      } else {
        setError(result.error || 'An error occurred')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, [url, immediate])

  return { data, loading, error, execute, refetch: execute }
}

export function useApiMutation<T, P = any>() {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const mutate = async (
    url: string,
    payload?: P,
    options: RequestInit = {}
  ) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        body: payload ? JSON.stringify(payload) : undefined,
        ...options,
      })

      const result: ApiResponse<T> = await response.json()

      if (result.success) {
        setData(result.data || null)
        return result
      } else {
        setError(result.error || 'An error occurred')
        throw new Error(result.error || 'An error occurred')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { data, loading, error, mutate }
}
