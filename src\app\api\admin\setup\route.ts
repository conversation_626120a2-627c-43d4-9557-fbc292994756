/**
 * Admin Setup API
 * 
 * Creates the initial admin user for first-time setup.
 * This endpoint should be secured or disabled after initial setup.
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import connectDB from '@/lib/mongodb'
import { User } from '@/lib/models'
import { ApiResponse } from '@/types'
import { getZodErrorMessage } from '@/utils'

// Validation schema for admin setup
const setupSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirmPassword: z.string(),
  setupKey: z.string().min(1, 'Setup key is required'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = setupSchema.parse(body)
    
    // Check setup key (you should set this in your environment variables)
    const expectedSetupKey = process.env.ADMIN_SETUP_KEY || 'admin-setup-2024'
    if (validatedData.setupKey !== expectedSetupKey) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid setup key',
      }
      return NextResponse.json(response, { status: 403 })
    }
    
    // Connect to database
    await connectDB()
    
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ role: 'admin' })
    if (existingAdmin) {
      const response: ApiResponse = {
        success: false,
        error: 'Admin user already exists. Setup has already been completed.',
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // Check if email is already taken
    const existingUser = await User.findOne({ email: validatedData.email.toLowerCase() })
    if (existingUser) {
      const response: ApiResponse = {
        success: false,
        error: 'Email address is already registered',
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // Create admin user (password will be hashed by the User model's pre-save hook)
    const adminUser = new User({
      name: validatedData.name,
      email: validatedData.email.toLowerCase(),
      password: validatedData.password, // Don't hash here - let the model do it
      role: 'admin',
      emailVerified: new Date(), // Auto-verify admin email
    })
    
    await adminUser.save()
    
    const response: ApiResponse = {
      success: true,
      message: 'Admin user created successfully! You can now log in.',
      data: {
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role,
      },
    }
    
    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Admin setup error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create admin user. Please try again.',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Check if admin user exists
    const adminExists = await User.findOne({ role: 'admin' })
    
    const response: ApiResponse = {
      success: true,
      data: {
        setupRequired: !adminExists,
        hasAdmin: !!adminExists,
      },
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Admin setup check error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to check setup status',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
