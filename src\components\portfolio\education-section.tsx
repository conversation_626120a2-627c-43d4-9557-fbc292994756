'use client'

import { motion } from 'framer-motion'
import { Calendar, MapPin, GraduationCap } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDate } from '@/utils'

// Education data based on your resume
const education = [
  {
    id: '1',
    degree: 'Bachelor of Science (BSc.) Mathematics',
    institution: 'University of Ibadan',
    location: 'Ibadan, Nigeria',
    startDate: new Date('2018-03-01'),
    endDate: new Date('2023-07-01'),
    current: false,
    description: 'Comprehensive study of mathematical principles, statistics, and analytical problem-solving methods that enhanced logical thinking and systematic approach to complex problems.'
  },
  {
    id: '2',
    degree: 'Foundation Degree Engineering and Computing',
    institution: 'Lancaster University',
    location: 'Lancaster, United Kingdom',
    startDate: new Date('2015-09-01'),
    endDate: new Date('2016-07-01'),
    current: false,
    description: 'Foundation program covering engineering principles and computing fundamentals, providing a strong technical foundation for software development career.'
  }
]

// Certifications data
const certifications = [
  {
    id: '1',
    name: 'Full Stack Web Development',
    issuer: 'SQI College of ICT',
    issueDate: new Date('2023-01-01'),
    description: 'Comprehensive certification covering modern web development technologies and best practices.'
  },
  {
    id: '2',
    name: 'Various Technical Certifications',
    issuer: 'Coursera | Udemy',
    issueDate: new Date('2022-01-01'),
    description: 'Multiple certifications in cloud computing, modern frameworks, and software engineering practices.'
  }
]

interface EducationSectionProps {
  showCertifications?: boolean
}

export function EducationSection({ showCertifications = true }: EducationSectionProps) {
  return (
    <div className="space-y-8">
      {/* Education */}
      <div className="space-y-6">
        <h3 className="text-2xl font-bold flex items-center gap-2">
          <GraduationCap className="h-6 w-6 text-primary" />
          Education
        </h3>
        
        <div className="space-y-4">
          {education.map((edu, index) => (
            <motion.div
              key={edu.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                      <CardTitle className="text-lg">{edu.degree}</CardTitle>
                      <div className="flex items-center gap-2 text-primary font-medium">
                        <span>{edu.institution}</span>
                        <span>•</span>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          <span>{edu.location}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>
                        {formatDate(edu.startDate, { month: 'short', year: 'numeric' })} - {' '}
                        {formatDate(edu.endDate, { month: 'short', year: 'numeric' })}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                {edu.description && (
                  <CardContent>
                    <p className="text-muted-foreground">{edu.description}</p>
                  </CardContent>
                )}
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Certifications */}
      {showCertifications && (
        <div className="space-y-6">
          <h3 className="text-2xl font-bold flex items-center gap-2">
            <div className="w-6 h-6 bg-primary rounded flex items-center justify-center">
              <span className="text-primary-foreground text-xs font-bold">C</span>
            </div>
            Certifications
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {certifications.map((cert, index) => (
              <motion.div
                key={cert.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              >
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle className="text-lg">{cert.name}</CardTitle>
                    <div className="text-primary font-medium">{cert.issuer}</div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(cert.issueDate, { year: 'numeric' })}</span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm">{cert.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
