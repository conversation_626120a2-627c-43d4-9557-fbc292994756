import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import connectDB from '@/lib/mongodb'
import { Contact } from '@/lib/models'
import { sendContactNotification, sendContactAutoReply } from '@/lib/email'
import { ApiResponse } from '@/types'
import { getZodErrorMessage } from '@/utils'

// Validation schema
const contactSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject cannot exceed 200 characters'),
  message: z.string().min(1, 'Message is required').max(2000, 'Message cannot exceed 2000 characters'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = contactSchema.parse(body)
    
    // Connect to database
    await connectDB()
    
    // Create contact entry
    const contact = new Contact(validatedData)
    await contact.save()
    
    // Send notification emails
    try {
      await Promise.all([
        sendContactNotification(validatedData),
        sendContactAutoReply(validatedData.email, validatedData.name),
      ])
    } catch (emailError) {
      console.error('Error sending emails:', emailError)
      // Don't fail the request if email fails
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'Message sent successfully! I\'ll get back to you soon.',
    }
    
    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Contact form error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Something went wrong. Please try again later.',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    
    await connectDB()
    
    // Build query
    const query: any = {}
    if (status && ['new', 'read', 'replied', 'archived'].includes(status)) {
      query.status = status
    }
    
    // Get contacts with pagination
    const skip = (page - 1) * limit
    const contacts = await Contact.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()
    
    const total = await Contact.countDocuments(query)
    
    const response: ApiResponse = {
      success: true,
      data: {
        contacts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Get contacts error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch contacts',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
