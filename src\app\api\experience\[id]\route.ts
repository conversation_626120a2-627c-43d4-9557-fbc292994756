import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import connectDB from '@/lib/mongodb'
import { Experience } from '@/lib/models'
import { requireAdmin } from '@/lib/auth'
import { ApiResponse } from '@/types'
import { getZodErrorMessage } from '@/utils'

// Validation schema
const experienceSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title cannot exceed 100 characters'),
  company: z.string().min(1, 'Company is required').max(100, 'Company cannot exceed 100 characters'),
  location: z.string().min(1, 'Location is required').max(100, 'Location cannot exceed 100 characters'),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z.string().transform((str) => new Date(str)).optional(),
  current: z.boolean().default(false),
  description: z.array(z.string()).min(1, 'At least one description point is required'),
  technologies: z.array(z.string()).default([]),
  order: z.number().default(0),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authentication
    const authResult = await requireAdmin(request)
    if ('error' in authResult) {
      const response: ApiResponse = {
        success: false,
        error: authResult.error,
      }
      return NextResponse.json(response, { status: authResult.status })
    }
    
    const body = await request.json()
    
    // Validate input
    const validatedData = experienceSchema.parse(body)
    
    // Connect to database
    await connectDB()
    
    // Update experience
    const experience = await Experience.findByIdAndUpdate(
      params.id,
      validatedData,
      { new: true, runValidators: true }
    )
    
    if (!experience) {
      const response: ApiResponse = {
        success: false,
        error: 'Experience not found',
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      data: experience,
      message: 'Experience updated successfully',
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Update experience error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to update experience',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authentication
    const authResult = await requireAdmin(request)
    if ('error' in authResult) {
      const response: ApiResponse = {
        success: false,
        error: authResult.error,
      }
      return NextResponse.json(response, { status: authResult.status })
    }
    
    // Connect to database
    await connectDB()
    
    // Delete experience
    const experience = await Experience.findByIdAndDelete(params.id)
    
    if (!experience) {
      const response: ApiResponse = {
        success: false,
        error: 'Experience not found',
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'Experience deleted successfully',
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Delete experience error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to delete experience',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
