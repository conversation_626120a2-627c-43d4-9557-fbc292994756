/**
 * Add New Project Page
 * 
 * Form for creating new projects with comprehensive validation.
 */

'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft, Save, Plus, X } from 'lucide-react'
import Link from 'next/link'
import { toast } from 'react-hot-toast'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { useApiMutation } from '@/hooks/use-api'

interface ProjectFormData {
  title: string
  description: string
  longDescription: string
  technologies: string[]
  category: 'web' | 'mobile' | 'wordpress' | 'other'
  featured: boolean
  liveUrl: string
  githubUrl: string
  demoUrl: string
  order: number
}

const initialFormData: ProjectFormData = {
  title: '',
  description: '',
  longDescription: '',
  technologies: [],
  category: 'web',
  featured: false,
  liveUrl: '',
  githubUrl: '',
  demoUrl: '',
  order: 1,
}

export default function NewProjectPage(): JSX.Element {
  const router = useRouter()
  const [formData, setFormData] = useState<ProjectFormData>(initialFormData)
  const [newTechnology, setNewTechnology] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  const { mutate: createProject, loading } = useApiMutation()

  const handleInputChange = (field: keyof ProjectFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const value = e.target.type === 'checkbox' ? (e.target as HTMLInputElement).checked : e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear field error
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const addTechnology = () => {
    if (newTechnology.trim() && !formData.technologies.includes(newTechnology.trim())) {
      setFormData(prev => ({
        ...prev,
        technologies: [...prev.technologies, newTechnology.trim()]
      }))
      setNewTechnology('')
    }
  }

  const removeTechnology = (tech: string) => {
    setFormData(prev => ({
      ...prev,
      technologies: prev.technologies.filter(t => t !== tech)
    }))
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    }

    if (!formData.longDescription.trim()) {
      newErrors.longDescription = 'Long description is required'
    }

    if (formData.technologies.length === 0) {
      newErrors.technologies = 'At least one technology is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      await createProject('/api/projects', formData)
      toast.success('Project created successfully!')
      router.push('/admin/projects')
    } catch (error) {
      toast.error('Failed to create project')
    }
  }

  return (
    <AdminLayout 
      title="Add New Project"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Projects', href: '/admin/projects' },
        { label: 'New Project' },
      ]}
    >
      <div className="max-w-4xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Button variant="ghost" asChild>
            <Link href="/admin/projects">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Link>
          </Button>
        </motion.div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label htmlFor="title" className="block text-sm font-medium mb-2">
                      Project Title *
                    </label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={handleInputChange('title')}
                      placeholder="Enter project title"
                      className={errors.title ? 'border-destructive' : ''}
                    />
                    {errors.title && (
                      <p className="text-sm text-destructive mt-1">{errors.title}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="category" className="block text-sm font-medium mb-2">
                      Category *
                    </label>
                    <select
                      id="category"
                      value={formData.category}
                      onChange={handleInputChange('category')}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="web">Web Application</option>
                      <option value="mobile">Mobile Application</option>
                      <option value="wordpress">WordPress</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="order" className="block text-sm font-medium mb-2">
                      Display Order
                    </label>
                    <Input
                      id="order"
                      type="number"
                      value={formData.order}
                      onChange={handleInputChange('order')}
                      placeholder="1"
                      min="1"
                    />
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium mb-2">
                    Short Description *
                  </label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={handleInputChange('description')}
                    placeholder="Brief description of the project"
                    rows={3}
                    className={errors.description ? 'border-destructive' : ''}
                  />
                  {errors.description && (
                    <p className="text-sm text-destructive mt-1">{errors.description}</p>
                  )}
                </div>

                {/* Long Description */}
                <div>
                  <label htmlFor="longDescription" className="block text-sm font-medium mb-2">
                    Detailed Description *
                  </label>
                  <Textarea
                    id="longDescription"
                    value={formData.longDescription}
                    onChange={handleInputChange('longDescription')}
                    placeholder="Detailed description of the project, features, and technical details"
                    rows={6}
                    className={errors.longDescription ? 'border-destructive' : ''}
                  />
                  {errors.longDescription && (
                    <p className="text-sm text-destructive mt-1">{errors.longDescription}</p>
                  )}
                </div>

                {/* Technologies */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Technologies Used *
                  </label>
                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <Input
                        value={newTechnology}
                        onChange={(e) => setNewTechnology(e.target.value)}
                        placeholder="Add technology (e.g., React, Node.js)"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTechnology())}
                      />
                      <Button type="button" onClick={addTechnology} variant="outline">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {formData.technologies.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {formData.technologies.map((tech) => (
                          <span
                            key={tech}
                            className="inline-flex items-center gap-1 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
                          >
                            {tech}
                            <button
                              type="button"
                              onClick={() => removeTechnology(tech)}
                              className="hover:text-destructive"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </span>
                        ))}
                      </div>
                    )}
                    
                    {errors.technologies && (
                      <p className="text-sm text-destructive">{errors.technologies}</p>
                    )}
                  </div>
                </div>

                {/* URLs */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="liveUrl" className="block text-sm font-medium mb-2">
                      Live URL
                    </label>
                    <Input
                      id="liveUrl"
                      type="url"
                      value={formData.liveUrl}
                      onChange={handleInputChange('liveUrl')}
                      placeholder="https://example.com"
                    />
                  </div>

                  <div>
                    <label htmlFor="githubUrl" className="block text-sm font-medium mb-2">
                      GitHub URL
                    </label>
                    <Input
                      id="githubUrl"
                      type="url"
                      value={formData.githubUrl}
                      onChange={handleInputChange('githubUrl')}
                      placeholder="https://github.com/username/repo"
                    />
                  </div>

                  <div>
                    <label htmlFor="demoUrl" className="block text-sm font-medium mb-2">
                      Demo URL
                    </label>
                    <Input
                      id="demoUrl"
                      type="url"
                      value={formData.demoUrl}
                      onChange={handleInputChange('demoUrl')}
                      placeholder="https://demo.example.com"
                    />
                  </div>
                </div>

                {/* Featured */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => 
                      setFormData(prev => ({ ...prev, featured: checked as boolean }))
                    }
                  />
                  <label htmlFor="featured" className="text-sm font-medium">
                    Featured Project
                  </label>
                </div>

                {/* Submit Buttons */}
                <div className="flex gap-4 pt-6">
                  <Button type="submit" disabled={loading} className="flex-1">
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Create Project
                      </>
                    )}
                  </Button>
                  <Button type="button" variant="outline" asChild>
                    <Link href="/admin/projects">Cancel</Link>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AdminLayout>
  )
}
