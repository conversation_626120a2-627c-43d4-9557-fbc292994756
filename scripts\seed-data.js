/**
 * Seed Data Script
 *
 * Seeds the database with real data about <PERSON><PERSON> - Software Engineer
 * This creates authentic portfolio content that can be managed via admin CRUD.
 */

const { MongoClient, ObjectId } = require('mongodb')
require('dotenv').config({ path: '.env.local' })

async function seedData() {
  const mongoUri = process.env.MONGODB_URI
  
  if (!mongoUri) {
    console.error('❌ MONGODB_URI not found in environment variables')
    process.exit(1)
  }

  let client

  try {
    console.log('🌱 Seeding database with sample data...\n')
    
    // Connect to MongoDB
    client = new MongoClient(mongoUri)
    await client.connect()
    
    const db = client.db()
    
    // Real projects by <PERSON><PERSON> - Software Engineer
    const projects = [
      {
        _id: new ObjectId(),
        title: 'Smooth Beauty',
        description: 'Comprehensive beauty marketplace with multi-platform applications for users, vendors, admins, and delivery personnel',
        longDescription: 'Smooth Beauty is a complete beauty marketplace ecosystem featuring four interconnected applications: a customer mobile app for browsing and purchasing beauty products, a vendor web dashboard for inventory and order management, an admin panel for platform oversight, and a delivery app for order fulfillment. The platform includes real-time chat, payment processing with Stripe, order tracking, and comprehensive analytics.',
        technologies: ['React', 'React Native', 'Node.js', 'MongoDB', 'Express.js', 'Socket.io', 'Stripe', 'Firebase', 'Redux', 'TypeScript'],
        category: 'web',
        featured: true,
        liveUrl: 'https://smoothbeauty.com',
        githubUrl: 'https://github.com/seyiobadeyi/smooth-beauty',
        demoUrl: '',
        order: 1,
        createdAt: new Date('2024-03-15'),
        updatedAt: new Date('2024-03-15')
      },
      {
        _id: new ObjectId(),
        title: 'Crowned Naturally',
        description: 'E-commerce platform specializing in natural hair care products with advanced product filtering and customer reviews',
        longDescription: 'Crowned Naturally is a specialized e-commerce platform for natural hair care products. Built with Next.js and TypeScript, it features advanced product filtering by hair type and concerns, customer review system, wishlist functionality, and secure payment processing. The platform includes an admin dashboard for inventory management and order processing.',
        technologies: ['Next.js', 'TypeScript', 'Prisma', 'PostgreSQL', 'Tailwind CSS', 'Stripe', 'NextAuth.js', 'Vercel'],
        category: 'web',
        featured: true,
        liveUrl: 'https://crownednaturally.com',
        githubUrl: 'https://github.com/seyiobadeyi/crowned-naturally',
        demoUrl: '',
        order: 2,
        createdAt: new Date('2024-02-20'),
        updatedAt: new Date('2024-02-20')
      },
      {
        _id: new ObjectId(),
        title: 'Shoppoint',
        description: 'Multi-vendor marketplace with separate mobile applications for customers and service providers',
        longDescription: 'Shoppoint is a comprehensive multi-vendor marketplace featuring dual mobile applications built with React Native. The customer app allows users to browse services, book appointments, and make payments, while the provider app enables service providers to manage their listings, schedules, and earnings. The platform includes real-time notifications, in-app messaging, and integrated payment processing.',
        technologies: ['React Native', 'Node.js', 'MongoDB', 'Express.js', 'Firebase', 'Redux', 'Socket.io', 'Stripe'],
        category: 'mobile',
        featured: true,
        liveUrl: '',
        githubUrl: 'https://github.com/seyiobadeyi/shoppoint',
        demoUrl: '',
        order: 3,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-10')
      },
      {
        _id: new ObjectId(),
        title: 'Portfolio Website',
        description: 'Modern portfolio website with admin dashboard, blog system, and contact management',
        longDescription: 'A comprehensive portfolio website built with Next.js 14 and TypeScript, featuring a complete admin dashboard for content management, blog system with rich text editing, contact form management, and newsletter subscription handling. The site includes dark/light theme support, responsive design, and SEO optimization.',
        technologies: ['Next.js', 'TypeScript', 'MongoDB', 'NextAuth.js', 'Tailwind CSS', 'Framer Motion', 'React Hook Form', 'Zod'],
        category: 'web',
        featured: true,
        liveUrl: 'https://seyiobadeyi.com',
        githubUrl: 'https://github.com/seyiobadeyi/portfolio',
        demoUrl: '',
        order: 4,
        createdAt: new Date('2024-08-05'),
        updatedAt: new Date('2024-08-05')
      },
      {
        _id: new ObjectId(),
        title: 'Task Management API',
        description: 'RESTful API for task and project management with team collaboration features',
        longDescription: 'A robust RESTful API built with Node.js and Express.js for task and project management. Features include user authentication with JWT, role-based access control, real-time notifications, file attachments, and comprehensive project analytics. The API supports team collaboration with commenting, task assignments, and progress tracking.',
        technologies: ['Node.js', 'Express.js', 'MongoDB', 'JWT', 'Multer', 'Socket.io', 'Jest', 'Swagger'],
        category: 'web',
        featured: false,
        liveUrl: '',
        githubUrl: 'https://github.com/seyiobadeyi/task-api',
        demoUrl: '',
        order: 5,
        createdAt: new Date('2023-11-20'),
        updatedAt: new Date('2023-11-20')
      },
      {
        _id: new ObjectId(),
        title: 'Weather Dashboard',
        description: 'Real-time weather dashboard with location-based forecasts and weather alerts',
        longDescription: 'An interactive weather dashboard that provides real-time weather information, 7-day forecasts, and severe weather alerts. Built with React and integrated with multiple weather APIs for accurate data. Features include location search, favorite locations, weather maps, and responsive design for mobile and desktop use.',
        technologies: ['React', 'JavaScript', 'OpenWeather API', 'Chart.js', 'CSS3', 'Local Storage'],
        category: 'web',
        featured: false,
        liveUrl: 'https://weather-dashboard-seyi.vercel.app',
        githubUrl: 'https://github.com/seyiobadeyi/weather-dashboard',
        demoUrl: '',
        order: 6,
        createdAt: new Date('2023-09-15'),
        updatedAt: new Date('2023-09-15')
      }
    ]
    
    // Real contact inquiries
    const contacts = [
      {
        _id: new ObjectId(),
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        subject: 'Full-Stack Development Opportunity',
        message: 'Hi Seyi, I came across your portfolio and I\'m impressed with your work on Smooth Beauty and Crowned Naturally. We\'re a fintech startup looking for a skilled software engineer to join our team. Would you be interested in discussing a full-time opportunity? We offer competitive compensation and equity.',
        read: false,
        createdAt: new Date('2024-08-04T10:30:00Z'),
        updatedAt: new Date('2024-08-04T10:30:00Z')
      },
      {
        _id: new ObjectId(),
        name: 'David Chen',
        email: '<EMAIL>',
        subject: 'E-commerce Project Collaboration',
        message: 'Hello Seyi, I\'m the CTO at Creative Studio and we have a client who needs an e-commerce platform similar to your Crowned Naturally project. Would you be available for a freelance project? The timeline is 3 months and budget is flexible for the right developer.',
        read: true,
        createdAt: new Date('2024-08-03T14:15:00Z'),
        updatedAt: new Date('2024-08-03T16:20:00Z')
      },
      {
        _id: new ObjectId(),
        name: 'Maria Rodriguez',
        email: '<EMAIL>',
        subject: 'Technical Architecture Consultation',
        message: 'Hi Seyi, I\'m leading the tech team at BeautyTech and we\'re building a marketplace similar to Smooth Beauty. I\'d love to get your insights on the technical architecture and challenges you faced. Would you be open to a paid consultation call?',
        read: false,
        createdAt: new Date('2024-08-02T09:45:00Z'),
        updatedAt: new Date('2024-08-02T09:45:00Z')
      },
      {
        _id: new ObjectId(),
        name: 'James Wilson',
        email: '<EMAIL>',
        subject: 'React Native Development Partnership',
        message: 'Hello, I saw your Shoppoint project and I\'m impressed with the React Native implementation. We\'re an agency specializing in mobile apps and we\'re looking for experienced React Native developers for ongoing partnerships. Are you interested in discussing project-based collaborations?',
        read: true,
        createdAt: new Date('2024-08-01T11:20:00Z'),
        updatedAt: new Date('2024-08-01T13:45:00Z')
      },
      {
        _id: new ObjectId(),
        name: 'Emily Thompson',
        email: '<EMAIL>',
        subject: 'Senior Software Engineer Position',
        message: 'Hi Seyi, I\'m a technical recruiter at HRTech Solutions. We have a senior software engineer position that would be perfect for someone with your full-stack expertise. The role involves building scalable web applications with React and Node.js. Salary range is $120k-$150k plus benefits. Interested in learning more?',
        read: false,
        createdAt: new Date('2024-07-31T16:30:00Z'),
        updatedAt: new Date('2024-07-31T16:30:00Z')
      }
    ]
    
    // Newsletter subscribers interested in software engineering content
    const newsletters = [
      {
        _id: new ObjectId(),
        email: '<EMAIL>',
        name: 'Alex Thompson',
        subscribed: true,
        source: 'portfolio_website',
        interests: ['React', 'Node.js', 'Full-Stack Development'],
        createdAt: new Date('2024-07-15T08:30:00Z'),
        updatedAt: new Date('2024-07-15T08:30:00Z')
      },
      {
        _id: new ObjectId(),
        email: '<EMAIL>',
        name: 'Priya Patel',
        subscribed: true,
        source: 'blog_post',
        interests: ['React Native', 'Mobile Development', 'TypeScript'],
        createdAt: new Date('2024-07-20T14:45:00Z'),
        updatedAt: new Date('2024-07-20T14:45:00Z')
      },
      {
        _id: new ObjectId(),
        email: '<EMAIL>',
        name: 'Carlos Rodriguez',
        subscribed: true,
        source: 'github_profile',
        interests: ['Frontend Development', 'UI/UX', 'Next.js'],
        createdAt: new Date('2024-07-25T10:15:00Z'),
        updatedAt: new Date('2024-07-25T10:15:00Z')
      },
      {
        _id: new ObjectId(),
        email: '<EMAIL>',
        name: 'Lisa Chen',
        subscribed: true,
        source: 'portfolio_website',
        interests: ['Backend Development', 'APIs', 'Database Design'],
        createdAt: new Date('2024-07-28T16:20:00Z'),
        updatedAt: new Date('2024-07-28T16:20:00Z')
      },
      {
        _id: new ObjectId(),
        email: '<EMAIL>',
        name: 'Michael Johnson',
        subscribed: true,
        source: 'social_media',
        interests: ['Full-Stack Development', 'DevOps', 'Cloud Computing'],
        createdAt: new Date('2024-08-01T12:00:00Z'),
        updatedAt: new Date('2024-08-01T12:00:00Z')
      }
    ]
    
    // Technical blog posts by Seyi Obadeyi - Software Engineer
    const blogPosts = [
      {
        _id: new ObjectId(),
        title: 'Building Scalable E-commerce Platforms with React and Node.js',
        slug: 'building-scalable-ecommerce-platforms-react-nodejs',
        excerpt: 'Lessons learned from building Smooth Beauty and Crowned Naturally - two production e-commerce platforms serving thousands of users.',
        content: `# Building Scalable E-commerce Platforms with React and Node.js

As a software engineer who has built multiple e-commerce platforms including Smooth Beauty and Crowned Naturally, I've learned valuable lessons about creating scalable, maintainable applications that can handle real-world traffic and business requirements.

## Architecture Decisions

When building e-commerce platforms, the architecture decisions you make early on will significantly impact your ability to scale. Here are the key considerations:

### Frontend Architecture
- **React with TypeScript**: Provides type safety and better developer experience
- **State Management**: Redux for complex state, Context API for simpler scenarios
- **Component Library**: Build reusable components from the start

### Backend Architecture
- **Node.js with Express**: Fast development and JavaScript everywhere
- **MongoDB**: Flexible schema for product catalogs
- **Microservices**: Separate services for payments, notifications, and inventory

## Performance Optimization

Performance is crucial for e-commerce success. Here's what I've implemented:

1. **Image Optimization**: WebP format with fallbacks
2. **Lazy Loading**: Components and images load on demand
3. **Caching**: Redis for session storage and frequently accessed data
4. **CDN**: Static assets served from edge locations

## Security Considerations

E-commerce platforms handle sensitive data, so security is paramount:

- **Authentication**: JWT tokens with refresh mechanism
- **Payment Security**: PCI compliance with Stripe integration
- **Data Validation**: Zod for runtime type checking
- **Rate Limiting**: Prevent abuse and DDoS attacks

## Lessons Learned

Building production e-commerce platforms taught me:

1. **Start with MVP**: Don't over-engineer initially
2. **Monitor Everything**: Implement logging and analytics early
3. **Test Thoroughly**: Unit, integration, and e2e tests
4. **Plan for Scale**: Design with growth in mind

The journey of building these platforms has been incredibly rewarding, and I'm excited to share more insights in future posts.`,
        status: 'published',
        featured: true,
        tags: ['React', 'Node.js', 'E-commerce', 'Architecture', 'Scalability'],
        author: 'Seyi Obadeyi',
        readTime: 8,
        views: 1247,
        likes: 89,
        publishedAt: new Date('2024-07-15T10:00:00Z'),
        createdAt: new Date('2024-07-10T14:30:00Z'),
        updatedAt: new Date('2024-07-15T10:00:00Z')
      },
      {
        _id: new ObjectId(),
        title: 'React Native vs Flutter: A Software Engineer\'s Perspective',
        slug: 'react-native-vs-flutter-software-engineer-perspective',
        excerpt: 'Having built mobile apps with React Native, here\'s my honest comparison with Flutter and when to choose each framework.',
        content: `# React Native vs Flutter: A Software Engineer's Perspective

As a software engineer who has built production mobile applications with React Native (including the Shoppoint marketplace), I often get asked about the React Native vs Flutter debate. Here's my honest take based on real-world experience.

## React Native: The JavaScript Advantage

### Pros:
- **Code Sharing**: Share logic between web and mobile
- **Developer Pool**: Easier to find React developers
- **Ecosystem**: Mature package ecosystem
- **Hot Reload**: Fast development iteration

### Cons:
- **Performance**: Bridge communication overhead
- **Platform Differences**: iOS and Android inconsistencies
- **Native Modules**: Sometimes need platform-specific code

## Flutter: The Dart Approach

### Pros:
- **Performance**: Compiled to native code
- **UI Consistency**: Same UI across platforms
- **Hot Reload**: Excellent development experience
- **Growing Ecosystem**: Rapidly expanding

### Cons:
- **Learning Curve**: New language (Dart)
- **Smaller Community**: Fewer developers and resources
- **App Size**: Larger bundle sizes

## When to Choose What

**Choose React Native when:**
- You have existing React expertise
- You want to share code with web
- You need extensive third-party integrations

**Choose Flutter when:**
- Performance is critical
- You want pixel-perfect UI consistency
- You're starting fresh without existing React codebase

## My Recommendation

For most projects, I still lean towards React Native due to the JavaScript ecosystem and developer availability. However, Flutter is rapidly closing the gap and might be the better choice for performance-critical applications.

The best framework is the one your team can execute well with.`,
        status: 'published',
        featured: true,
        tags: ['React Native', 'Flutter', 'Mobile Development', 'Comparison'],
        author: 'Seyi Obadeyi',
        readTime: 6,
        views: 892,
        likes: 67,
        publishedAt: new Date('2024-06-20T09:00:00Z'),
        createdAt: new Date('2024-06-15T11:20:00Z'),
        updatedAt: new Date('2024-06-20T09:00:00Z')
      },
      {
        _id: new ObjectId(),
        title: 'TypeScript Best Practices for Large-Scale Applications',
        slug: 'typescript-best-practices-large-scale-applications',
        excerpt: 'Essential TypeScript patterns and practices I use in production applications to maintain code quality and developer productivity.',
        content: `# TypeScript Best Practices for Large-Scale Applications

After working with TypeScript in multiple production applications, I've developed a set of best practices that help maintain code quality and developer productivity as projects scale.

## Type Safety First

### Strict Configuration
Always start with strict TypeScript configuration:

\`\`\`json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}
\`\`\`

### Utility Types
Leverage TypeScript's utility types:

\`\`\`typescript
// Pick specific properties
type UserProfile = Pick<User, 'name' | 'email' | 'avatar'>

// Make properties optional
type PartialUser = Partial<User>

// Exclude properties
type PublicUser = Omit<User, 'password' | 'internalId'>
\`\`\`

## API Integration

### Type-Safe API Calls
Define API response types:

\`\`\`typescript
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
}

interface User {
  id: string
  name: string
  email: string
}

const fetchUser = async (id: string): Promise<ApiResponse<User>> => {
  // Implementation
}
\`\`\`

## Component Patterns

### Generic Components
Build reusable components with generics:

\`\`\`typescript
interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  onRowClick?: (row: T) => void
}

function DataTable<T>({ data, columns, onRowClick }: DataTableProps<T>) {
  // Implementation
}
\`\`\`

## Error Handling

### Discriminated Unions
Use discriminated unions for error handling:

\`\`\`typescript
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E }

const processData = (): Result<ProcessedData> => {
  try {
    return { success: true, data: processedData }
  } catch (error) {
    return { success: false, error }
  }
}
\`\`\`

These practices have helped me build maintainable TypeScript applications that scale with team size and complexity.`,
        status: 'draft',
        featured: false,
        tags: ['TypeScript', 'Best Practices', 'Software Engineering', 'Code Quality'],
        author: 'Seyi Obadeyi',
        readTime: 10,
        views: 0,
        likes: 0,
        publishedAt: null,
        createdAt: new Date('2024-08-01T15:45:00Z'),
        updatedAt: new Date('2024-08-03T09:30:00Z')
      }
    ]
    
    // Insert data
    console.log('📁 Inserting projects...')
    await db.collection('projects').deleteMany({}) // Clear existing
    await db.collection('projects').insertMany(projects)
    console.log(`✅ Inserted ${projects.length} projects`)
    
    console.log('📧 Inserting contacts...')
    await db.collection('contacts').deleteMany({}) // Clear existing
    await db.collection('contacts').insertMany(contacts)
    console.log(`✅ Inserted ${contacts.length} contacts`)
    
    console.log('📰 Inserting newsletter subscribers...')
    await db.collection('newsletters').deleteMany({}) // Clear existing
    await db.collection('newsletters').insertMany(newsletters)
    console.log(`✅ Inserted ${newsletters.length} newsletter subscribers`)
    
    console.log('📝 Inserting blog posts...')
    await db.collection('blogposts').deleteMany({}) // Clear existing
    await db.collection('blogposts').insertMany(blogPosts)
    console.log(`✅ Inserted ${blogPosts.length} blog posts`)
    
    console.log('\n🎉 Database seeded successfully!')
    console.log('\n📊 Summary:')
    console.log(`  - ${projects.length} projects`)
    console.log(`  - ${contacts.length} contacts`)
    console.log(`  - ${newsletters.length} newsletter subscribers`)
    console.log(`  - ${blogPosts.length} blog posts`)
    
  } catch (error) {
    console.error('❌ Error seeding database:', error)
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// Run the seeding
seedData().catch(console.error)
