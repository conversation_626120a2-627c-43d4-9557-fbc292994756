import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import connectDB from '@/lib/mongodb'
import { BlogPost } from '@/lib/models'
import { requireAdmin } from '@/lib/auth'
import { ApiResponse } from '@/types'
import { generateSlug, getZodErrorMessage } from '@/utils'

// Validation schema
const blogPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title cannot exceed 200 characters'),
  slug: z.string().optional(),
  excerpt: z.string().min(1, 'Excerpt is required').max(300, 'Excerpt cannot exceed 300 characters'),
  content: z.string().min(1, 'Content is required'),
  featuredImage: z.string().optional(),
  tags: z.array(z.string()).default([]),
  category: z.string().min(1, 'Category is required').max(50, 'Category cannot exceed 50 characters'),
  published: z.boolean().default(false),
  seoTitle: z.string().max(60, 'SEO title cannot exceed 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description cannot exceed 160 characters').optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const category = searchParams.get('category')
    const tag = searchParams.get('tag')
    const search = searchParams.get('search')
    const published = searchParams.get('published')
    
    await connectDB()
    
    // Build query
    const query: any = {}
    
    // Only show published posts for public requests (no auth)
    const authHeader = request.headers.get('authorization')
    if (!authHeader && published !== 'false') {
      query.published = true
    } else if (published === 'true') {
      query.published = true
    } else if (published === 'false') {
      query.published = false
    }
    
    if (category) {
      query.category = category
    }
    
    if (tag) {
      query.tags = { $in: [tag] }
    }
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { excerpt: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ]
    }
    
    // Get blog posts with pagination
    const skip = (page - 1) * limit
    const posts = await BlogPost.find(query)
      .sort({ publishedAt: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean()
    
    const total = await BlogPost.countDocuments(query)
    
    const response: ApiResponse = {
      success: true,
      data: {
        posts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Get blog posts error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch blog posts',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const authResult = await requireAdmin(request)
    if ('error' in authResult) {
      const response: ApiResponse = {
        success: false,
        error: authResult.error,
      }
      return NextResponse.json(response, { status: authResult.status })
    }
    
    const body = await request.json()
    
    // Validate input
    const validatedData = blogPostSchema.parse(body)
    
    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.title)
    }
    
    // Connect to database
    await connectDB()
    
    // Check if slug already exists
    const existingPost = await BlogPost.findOne({ slug: validatedData.slug })
    if (existingPost) {
      const response: ApiResponse = {
        success: false,
        error: 'A post with this slug already exists',
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    // Create blog post
    const blogPost = new BlogPost(validatedData)
    await blogPost.save()
    
    const response: ApiResponse = {
      success: true,
      data: blogPost,
      message: 'Blog post created successfully',
    }
    
    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Create blog post error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create blog post',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
