import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import connectDB from '@/lib/mongodb'
import { Experience } from '@/lib/models'
import { requireAdmin } from '@/lib/auth'
import { ApiResponse } from '@/types'
import { getZodErrorMessage } from '@/utils'

// Validation schema
const experienceSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title cannot exceed 100 characters'),
  company: z.string().min(1, 'Company is required').max(100, 'Company cannot exceed 100 characters'),
  location: z.string().min(1, 'Location is required').max(100, 'Location cannot exceed 100 characters'),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z.string().transform((str) => new Date(str)).optional(),
  current: z.boolean().default(false),
  description: z.array(z.string()).min(1, 'At least one description point is required'),
  technologies: z.array(z.string()).default([]),
  order: z.number().default(0),
})

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const experiences = await Experience.find()
      .sort({ order: 1, startDate: -1 })
      .lean()
    
    const response: ApiResponse = {
      success: true,
      data: experiences,
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Get experiences error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch experiences',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const authResult = await requireAdmin(request)
    if ('error' in authResult) {
      const response: ApiResponse = {
        success: false,
        error: authResult.error,
      }
      return NextResponse.json(response, { status: authResult.status })
    }
    
    const body = await request.json()
    
    // Validate input
    const validatedData = experienceSchema.parse(body)
    
    // Connect to database
    await connectDB()
    
    // Create experience
    const experience = new Experience(validatedData)
    await experience.save()
    
    const response: ApiResponse = {
      success: true,
      data: experience,
      message: 'Experience created successfully',
    }
    
    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Create experience error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create experience',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
