import { Document } from 'mongoose'

// User Types
export interface I<PERSON><PERSON> extends Document {
  _id: string
  email: string
  password: string
  role: 'admin' | 'user'
  name: string
  createdAt: Date
  updatedAt: Date
}

// Portfolio Types
export interface IExperience extends Document {
  _id: string
  title: string
  company: string
  location: string
  startDate: Date
  endDate?: Date
  current: boolean
  description: string[]
  technologies: string[]
  order: number
  createdAt: Date
  updatedAt: Date
}

export interface IProject extends Document {
  _id: string
  title: string
  description: string
  longDescription?: string
  technologies: string[]
  category: 'web' | 'mobile' | 'wordpress' | 'other'
  featured: boolean
  images: string[]
  liveUrl?: string
  githubUrl?: string
  demoUrl?: string
  order: number
  createdAt: Date
  updatedAt: Date
}

export interface ISkill extends Document {
  _id: string
  name: string
  category: 'frontend' | 'backend' | 'database' | 'devops' | 'tools' | 'soft'
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  order: number
  createdAt: Date
  updatedAt: Date
}

export interface IEducation extends Document {
  _id: string
  degree: string
  institution: string
  location: string
  startDate: Date
  endDate?: Date
  current: boolean
  description?: string
  order: number
  createdAt: Date
  updatedAt: Date
}

export interface ICertification extends Document {
  _id: string
  name: string
  issuer: string
  issueDate: Date
  expiryDate?: Date
  credentialId?: string
  credentialUrl?: string
  order: number
  createdAt: Date
  updatedAt: Date
}

// Blog Types
export interface IBlogPost extends Document {
  _id: string
  title: string
  slug: string
  excerpt: string
  content: string
  featuredImage?: string
  tags: string[]
  category: string
  published: boolean
  publishedAt?: Date
  readTime: number
  views: number
  likes: number
  author: string
  seoTitle?: string
  seoDescription?: string
  createdAt: Date
  updatedAt: Date
}

// Contact Types
export interface IContact extends Document {
  _id: string
  name: string
  email: string
  subject: string
  message: string
  status: 'new' | 'read' | 'replied' | 'archived'
  createdAt: Date
  updatedAt: Date
}

// Newsletter Types
export interface INewsletter extends Document {
  _id: string
  email: string
  status: 'active' | 'unsubscribed'
  subscribedAt: Date
  unsubscribedAt?: Date
}

// Settings Types
export interface ISettings extends Document {
  _id: string
  key: string
  value: unknown
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  description?: string
  createdAt: Date
  updatedAt: Date
}

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// Form Types
export interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
}

export interface NewsletterFormData {
  email: string
}

export interface LoginFormData {
  email: string
  password: string
}

// Theme Types
export type Theme = 'light' | 'dark'

// Navigation Types
export interface NavItem {
  name: string
  href: string
  icon?: string
}

// Dashboard Types
export interface DashboardStats {
  totalProjects: number
  totalBlogPosts: number
  totalContacts: number
  totalNewsletterSubscribers: number
  recentContacts: IContact[]
  recentBlogPosts: IBlogPost[]
}
