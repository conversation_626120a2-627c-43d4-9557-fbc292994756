import mongoose, { Schema } from 'mongoose'
import { IExperience } from '@/types'

const ExperienceSchema = new Schema<IExperience>(
  {
    title: {
      type: String,
      required: [true, 'Job title is required'],
      trim: true,
      maxlength: [100, 'Title cannot exceed 100 characters'],
    },
    company: {
      type: String,
      required: [true, 'Company name is required'],
      trim: true,
      maxlength: [100, 'Company name cannot exceed 100 characters'],
    },
    location: {
      type: String,
      required: [true, 'Location is required'],
      trim: true,
      maxlength: [100, 'Location cannot exceed 100 characters'],
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required'],
    },
    endDate: {
      type: Date,
      validate: {
        validator: function (this: IExperience, value: Date) {
          return !value || value > this.startDate
        },
        message: 'End date must be after start date',
      },
    },
    current: {
      type: Boolean,
      default: false,
    },
    description: {
      type: [String],
      required: [true, 'Description is required'],
      validate: {
        validator: function (value: string[]) {
          return value.length > 0
        },
        message: 'At least one description point is required',
      },
    },
    technologies: {
      type: [String],
      default: [],
    },
    order: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
)

// Ensure only one current job
ExperienceSchema.pre('save', async function (next) {
  if (this.current && this.isModified('current')) {
    await mongoose.models.Experience.updateMany(
      { _id: { $ne: this._id } },
      { current: false }
    )
  }
  next()
})

// Index for ordering
ExperienceSchema.index({ order: 1, startDate: -1 })

export default mongoose.models.Experience || mongoose.model<IExperience>('Experience', ExperienceSchema)
