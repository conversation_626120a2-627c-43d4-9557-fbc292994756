import mongoose, { Schema } from 'mongoose'
import { IBlogPost } from '@/types'

const BlogPostSchema = new Schema<IBlogPost>(
  {
    title: {
      type: String,
      required: [true, 'Blog title is required'],
      trim: true,
      maxlength: [200, 'Title cannot exceed 200 characters'],
    },
    slug: {
      type: String,
      required: [true, 'Slug is required'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'],
    },
    excerpt: {
      type: String,
      required: [true, 'Excerpt is required'],
      trim: true,
      maxlength: [300, 'Excerpt cannot exceed 300 characters'],
    },
    content: {
      type: String,
      required: [true, 'Content is required'],
    },
    featuredImage: {
      type: String,
      trim: true,
    },
    tags: {
      type: [String],
      default: [],
      validate: {
        validator: function (value: string[]) {
          return value.every(tag => tag.length <= 30)
        },
        message: 'Each tag cannot exceed 30 characters',
      },
    },
    category: {
      type: String,
      required: [true, 'Category is required'],
      trim: true,
      maxlength: [50, 'Category cannot exceed 50 characters'],
    },
    published: {
      type: Boolean,
      default: false,
    },
    publishedAt: {
      type: Date,
    },
    readTime: {
      type: Number,
      default: 1,
      min: [1, 'Read time must be at least 1 minute'],
    },
    views: {
      type: Number,
      default: 0,
      min: [0, 'Views cannot be negative'],
    },
    likes: {
      type: Number,
      default: 0,
      min: [0, 'Likes cannot be negative'],
    },
    author: {
      type: String,
      required: [true, 'Author is required'],
      trim: true,
      default: 'Seyi Obadeyi',
    },
    seoTitle: {
      type: String,
      trim: true,
      maxlength: [60, 'SEO title cannot exceed 60 characters'],
    },
    seoDescription: {
      type: String,
      trim: true,
      maxlength: [160, 'SEO description cannot exceed 160 characters'],
    },
  },
  {
    timestamps: true,
  }
)

// Auto-generate slug from title if not provided
BlogPostSchema.pre('save', function (next) {
  if (!this.slug && this.title) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }
  
  // Set publishedAt when published
  if (this.published && !this.publishedAt) {
    this.publishedAt = new Date()
  }
  
  // Calculate read time based on content
  if (this.content) {
    const wordsPerMinute = 200
    const wordCount = this.content.split(/\s+/).length
    this.readTime = Math.ceil(wordCount / wordsPerMinute)
  }
  
  next()
})

// Indexes for performance
BlogPostSchema.index({ slug: 1 })
BlogPostSchema.index({ published: 1, publishedAt: -1 })
BlogPostSchema.index({ category: 1, published: 1 })
BlogPostSchema.index({ tags: 1, published: 1 })
BlogPostSchema.index({ views: -1, published: 1 })

export default mongoose.models.BlogPost || mongoose.model<IBlogPost>('BlogPost', BlogPostSchema)
