/**
 * Admin Newsletter Management Page
 * 
 * Provides comprehensive newsletter subscriber management including viewing,
 * filtering, bulk operations, and subscription analytics.
 */

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Users,
  UserPlus,
  UserMinus,
  Mail,
  Send,
  Download,
  Upload,
  Filter,
  Search,
  Calendar,
  TrendingUp,
  BarChart3,
  Trash2,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react'
import { format } from 'date-fns'
import { AdminLayout } from '@/components/admin/admin-layout'
import { DataTable } from '@/components/admin/data-table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useApi, useApiMutation } from '@/hooks/use-api'
import { toast } from 'react-hot-toast'
import type { INewsletter } from '@/types'

interface NewsletterTableData extends INewsletter {
  _id: string
  email: string
  status: 'active' | 'unsubscribed'
  subscribedAt: Date
  unsubscribedAt?: Date
  source?: string
}

/**
 * Admin Newsletter Page Component
 */
export default function AdminNewsletterPage(): JSX.Element {
  const [selectedSubscribers, setSelectedSubscribers] = useState<NewsletterTableData[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'unsubscribed'>('all')
  
  // API hooks
  const { data: newsletterData, loading, error, refetch } = useApi<{
    subscriptions: NewsletterTableData[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }>('/api/newsletter')
  
  const { mutate: updateSubscriber, loading: updating } = useApiMutation()
  const { mutate: deleteSubscriber, loading: deleting } = useApiMutation()

  // Handle row selection
  const handleRowSelect = (subscribers: NewsletterTableData[]) => {
    setSelectedSubscribers(subscribers)
  }

  // Handle subscriber status update
  const handleStatusUpdate = async (subscriberId: string, status: 'active' | 'unsubscribed') => {
    try {
      await updateSubscriber(`/api/newsletter/${subscriberId}`, { 
        status,
        unsubscribedAt: status === 'unsubscribed' ? new Date() : null
      }, 'PUT')
      toast.success(`Subscriber ${status === 'active' ? 'reactivated' : 'unsubscribed'} successfully`)
      refetch()
    } catch (error) {
      toast.error('Failed to update subscriber status')
    }
  }

  // Handle bulk status update
  const handleBulkStatusUpdate = async (status: 'active' | 'unsubscribed') => {
    try {
      await Promise.all(
        selectedSubscribers.map(subscriber => 
          updateSubscriber(`/api/newsletter/${subscriber._id}`, { 
            status,
            unsubscribedAt: status === 'unsubscribed' ? new Date() : null
          }, 'PUT')
        )
      )
      toast.success(`${selectedSubscribers.length} subscribers updated successfully`)
      setSelectedSubscribers([])
      refetch()
    } catch (error) {
      toast.error('Failed to update some subscribers')
    }
  }

  // Handle single subscriber deletion
  const handleDeleteSubscriber = async (subscriberId: string) => {
    if (!confirm('Are you sure you want to permanently delete this subscriber? This action cannot be undone.')) {
      return
    }

    try {
      await deleteSubscriber(`/api/newsletter/${subscriberId}`, {}, 'DELETE')
      toast.success('Subscriber deleted successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to delete subscriber')
    }
  }

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to permanently delete ${selectedSubscribers.length} subscribers? This action cannot be undone.`)) {
      return
    }

    try {
      await Promise.all(
        selectedSubscribers.map(subscriber => 
          deleteSubscriber(`/api/newsletter/${subscriber._id}`, {}, 'DELETE')
        )
      )
      toast.success(`${selectedSubscribers.length} subscribers deleted successfully`)
      setSelectedSubscribers([])
      refetch()
    } catch (error) {
      toast.error('Failed to delete some subscribers')
    }
  }

  // Handle export subscribers
  const handleExportSubscribers = () => {
    const activeSubscribers = newsletterData?.subscriptions?.filter(s => s.status === 'active') || []
    const csvContent = [
      'Email,Subscribed Date,Source',
      ...activeSubscribers.map(s => 
        `${s.email},${format(new Date(s.subscribedAt), 'yyyy-MM-dd')},${s.source || 'Website'}`
      )
    ].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `newsletter-subscribers-${format(new Date(), 'yyyy-MM-dd')}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
    
    toast.success('Subscribers exported successfully')
  }

  // Get status badge variant
  const getStatusBadgeVariant = (status: NewsletterTableData['status']) => {
    return status === 'active' ? 'default' : 'secondary'
  }

  // Table columns configuration
  const columns = [
    {
      key: 'email',
      label: 'Email Address',
      render: (subscriber: NewsletterTableData) => (
        <div className="font-medium">{subscriber.email}</div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (subscriber: NewsletterTableData) => (
        <Badge variant={getStatusBadgeVariant(subscriber.status)} className="gap-1">
          {subscriber.status === 'active' ? (
            <CheckCircle className="h-3 w-3" />
          ) : (
            <XCircle className="h-3 w-3" />
          )}
          {subscriber.status.charAt(0).toUpperCase() + subscriber.status.slice(1)}
        </Badge>
      ),
    },
    {
      key: 'subscribedAt',
      label: 'Subscribed',
      render: (subscriber: NewsletterTableData) => (
        <div className="text-sm">
          <div>{format(new Date(subscriber.subscribedAt), 'MMM dd, yyyy')}</div>
          <div className="text-muted-foreground">{format(new Date(subscriber.subscribedAt), 'HH:mm')}</div>
        </div>
      ),
    },
    {
      key: 'unsubscribedAt',
      label: 'Unsubscribed',
      render: (subscriber: NewsletterTableData) => (
        <div className="text-sm">
          {subscriber.unsubscribedAt ? (
            <>
              <div>{format(new Date(subscriber.unsubscribedAt), 'MMM dd, yyyy')}</div>
              <div className="text-muted-foreground">{format(new Date(subscriber.unsubscribedAt), 'HH:mm')}</div>
            </>
          ) : (
            <span className="text-muted-foreground">—</span>
          )}
        </div>
      ),
    },
    {
      key: 'source',
      label: 'Source',
      render: (subscriber: NewsletterTableData) => (
        <Badge variant="outline" className="text-xs">
          {subscriber.source || 'Website'}
        </Badge>
      ),
    },
  ]

  // Table actions configuration
  const actions = [
    {
      label: 'Reactivate',
      icon: UserPlus,
      onClick: (subscriber: NewsletterTableData) => handleStatusUpdate(subscriber._id, 'active'),
      show: (subscriber: NewsletterTableData) => subscriber.status === 'unsubscribed',
    },
    {
      label: 'Unsubscribe',
      icon: UserMinus,
      onClick: (subscriber: NewsletterTableData) => handleStatusUpdate(subscriber._id, 'unsubscribed'),
      show: (subscriber: NewsletterTableData) => subscriber.status === 'active',
    },
    {
      label: 'Send Test Email',
      icon: Mail,
      onClick: (subscriber: NewsletterTableData) => {
        // This would integrate with your email service
        toast.success(`Test email sent to ${subscriber.email}`)
      },
      show: (subscriber: NewsletterTableData) => subscriber.status === 'active',
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (subscriber: NewsletterTableData) => handleDeleteSubscriber(subscriber._id),
      variant: 'destructive' as const,
    },
  ]

  // Filter subscribers based on search and status
  const filteredSubscribers = newsletterData?.subscriptions?.filter(subscriber => {
    const matchesSearch = !searchQuery || 
      subscriber.email.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || subscriber.status === statusFilter
    
    return matchesSearch && matchesStatus
  }) || []

  return (
    <AdminLayout
      title="Newsletter Subscribers"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Newsletter' },
      ]}
    >
      <div className="space-y-6">
        {/* Header with Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid gap-4 md:grid-cols-4"
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{newsletterData?.subscriptions?.length || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Subscribers</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {newsletterData?.subscriptions?.filter(s => s.status === 'active').length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unsubscribed</CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {newsletterData?.subscriptions?.filter(s => s.status === 'unsubscribed').length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+12%</div>
              <p className="text-xs text-muted-foreground">vs last month</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Header Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex items-center justify-between"
        >
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Newsletter Subscribers</h2>
            <p className="text-muted-foreground">
              Manage your newsletter subscriber list and send campaigns
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {selectedSubscribers.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <MoreHorizontal className="h-4 w-4 mr-2" />
                    Bulk Actions ({selectedSubscribers.length})
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleBulkStatusUpdate('active')}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Reactivate
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkStatusUpdate('unsubscribed')}>
                    <UserMinus className="h-4 w-4 mr-2" />
                    Unsubscribe
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleBulkDelete}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            <Button variant="outline" onClick={handleExportSubscribers}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
            <Button>
              <Send className="h-4 w-4 mr-2" />
              Send Newsletter
            </Button>
          </div>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex items-center space-x-4"
        >
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search subscribers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Status: {statusFilter === 'all' ? 'All' : statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                All Subscribers
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('active')}>
                Active
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('unsubscribed')}>
                Unsubscribed
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </motion.div>

        {/* Newsletter Analytics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid gap-6 lg:grid-cols-3"
        >
          <div className="lg:col-span-2">
            <DataTable
              data={filteredSubscribers}
              columns={columns}
              loading={loading}
              error={error}
              pagination={newsletterData?.pagination}
              actions={actions}
              onRowSelect={handleRowSelect}
            />
          </div>

          <div className="space-y-6">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Quick Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Active Rate</span>
                  <span className="font-medium">
                    {newsletterData?.subscriptions?.length ?
                      Math.round((newsletterData.subscriptions.filter(s => s.status === 'active').length / newsletterData.subscriptions.length) * 100)
                      : 0}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">This Month</span>
                  <span className="font-medium text-green-600">+23</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">This Week</span>
                  <span className="font-medium text-green-600">+5</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Today</span>
                  <span className="font-medium text-green-600">+1</span>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center gap-2">
                    <UserPlus className="h-4 w-4 text-green-600" />
                    <span>New subscriber: <EMAIL></span>
                  </div>
                  <div className="flex items-center gap-2">
                    <UserMinus className="h-4 w-4 text-red-600" />
                    <span>Unsubscribed: <EMAIL></span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-blue-600" />
                    <span>Newsletter sent to 156 subscribers</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Newsletter Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Newsletter Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Send className="h-4 w-4 mr-2" />
                  Create Campaign
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Upload className="h-4 w-4 mr-2" />
                  Import Subscribers
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </Button>
              </CardContent>
            </Card>
          </div>
        </motion.div>
      </div>
    </AdminLayout>
  )
}
