/**
 * Admin Projects Management Page
 * 
 * Comprehensive project management interface with CRUD operations,
 * filtering, sorting, and bulk actions.
 */

'use client'

import { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Plus, Edit, Trash2, Eye, ExternalLink, Calendar } from 'lucide-react'
import Link from 'next/link'
import { toast } from 'react-hot-toast'
import { AdminLayout } from '@/components/admin/admin-layout'
import { DataTable } from '@/components/admin/data-table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useApi, useApiMutation } from '@/hooks/use-api'
import type { DataTableColumn } from '@/types/admin'
import type { IProject } from '@/types'

/**
 * Project data type for the table
 */
interface ProjectTableData extends IProject {
  _id: string
}

/**
 * Admin Projects Page Component
 */
export default function AdminProjectsPage(): JSX.Element {
  const [selectedProjects, setSelectedProjects] = useState<ProjectTableData[]>([])
  
  // API hooks
  const { data: projectsData, loading, error, refetch } = useApi<{
    projects: ProjectTableData[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }>('/api/projects')
  
  const { mutate: deleteProject, loading: deleteLoading } = useApiMutation()

  /**
   * Table column configuration
   */
  const columns: DataTableColumn<ProjectTableData>[] = [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <span className="text-sm font-medium text-primary">
              {String(value).charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <div className="font-medium">{String(value)}</div>
            <div className="text-sm text-muted-foreground">
              {row.category.charAt(0).toUpperCase() + row.category.slice(1)}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value) => (
        <div className="max-w-xs">
          <p className="text-sm text-muted-foreground line-clamp-2">
            {String(value)}
          </p>
        </div>
      ),
    },
    {
      key: 'technologies',
      label: 'Technologies',
      render: (value) => (
        <div className="flex flex-wrap gap-1">
          {(value as string[]).slice(0, 3).map((tech) => (
            <Badge key={tech} variant="secondary" className="text-xs">
              {tech}
            </Badge>
          ))}
          {(value as string[]).length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{(value as string[]).length - 3}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'featured',
      label: 'Featured',
      sortable: true,
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Featured' : 'Standard'}
        </Badge>
      ),
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-muted-foreground">
          {new Date(value as string).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: 'liveUrl',
      label: 'Status',
      render: (value, row) => (
        <div className="flex items-center space-x-2">
          {value && (
            <Badge variant="outline" className="text-xs">
              <ExternalLink className="w-3 h-3 mr-1" />
              Live
            </Badge>
          )}
          {row.githubUrl && (
            <Badge variant="outline" className="text-xs">
              GitHub
            </Badge>
          )}
        </div>
      ),
    },
  ]

  /**
   * Table actions configuration
   */
  const actions = [
    {
      label: 'View',
      icon: Eye,
      onClick: (project: ProjectTableData) => {
        window.open(`/projects/${project._id}`, '_blank')
      },
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: (project: ProjectTableData) => {
        // Navigate to edit page
        window.location.href = `/admin/projects/${project._id}/edit`
      },
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: handleDeleteProject,
      variant: 'destructive' as const,
    },
  ]

  /**
   * Handles project deletion with confirmation
   */
  async function handleDeleteProject(project: ProjectTableData) {
    if (!window.confirm(`Are you sure you want to delete "${project.title}"? This action cannot be undone.`)) {
      return
    }

    try {
      await deleteProject(`/api/projects/${project._id}`, undefined, { method: 'DELETE' })
      toast.success('Project deleted successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to delete project')
    }
  }

  /**
   * Handles bulk project deletion
   */
  const handleBulkDelete = useCallback(async () => {
    if (selectedProjects.length === 0) return

    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedProjects.length} project${
        selectedProjects.length !== 1 ? 's' : ''
      }? This action cannot be undone.`
    )

    if (!confirmed) return

    try {
      await Promise.all(
        selectedProjects.map(project =>
          deleteProject(`/api/projects/${project._id}`, undefined, { method: 'DELETE' })
        )
      )
      toast.success(`${selectedProjects.length} project${selectedProjects.length !== 1 ? 's' : ''} deleted successfully`)
      setSelectedProjects([])
      refetch()
    } catch (error) {
      toast.error('Failed to delete projects')
    }
  }, [selectedProjects, deleteProject, refetch])

  /**
   * Handles row selection
   */
  const handleRowSelect = useCallback((selectedRows: ProjectTableData[]) => {
    setSelectedProjects(selectedRows)
  }, [])

  return (
    <AdminLayout 
      title="Projects Management"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Projects' },
      ]}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Projects</h2>
            <p className="text-muted-foreground">
              Manage your portfolio projects and showcase your work
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {selectedProjects.length > 0 && (
              <Button
                variant="destructive"
                onClick={handleBulkDelete}
                disabled={deleteLoading}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected ({selectedProjects.length})
              </Button>
            )}
            <Button asChild>
              <Link href="/admin/projects/new">
                <Plus className="h-4 w-4 mr-2" />
                Add Project
              </Link>
            </Button>
          </div>
        </motion.div>

        {/* Projects Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <DataTable
            data={projectsData?.projects || []}
            columns={columns}
            loading={loading}
            error={error}
            pagination={projectsData?.pagination}
            actions={actions}
            onRowSelect={handleRowSelect}
            onRowClick={(project) => {
              window.location.href = `/admin/projects/${project._id}/edit`
            }}
          />
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid gap-4 md:grid-cols-4"
        >
          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Projects</p>
                <p className="text-2xl font-bold">{projectsData?.pagination?.total || 0}</p>
              </div>
              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                <Calendar className="h-4 w-4 text-primary" />
              </div>
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Featured</p>
                <p className="text-2xl font-bold">
                  {projectsData?.projects?.filter(p => p.featured).length || 0}
                </p>
              </div>
              <div className="w-8 h-8 bg-yellow-500/10 rounded-lg flex items-center justify-center">
                <Calendar className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">With Live URL</p>
                <p className="text-2xl font-bold">
                  {projectsData?.projects?.filter(p => p.liveUrl).length || 0}
                </p>
              </div>
              <div className="w-8 h-8 bg-green-500/10 rounded-lg flex items-center justify-center">
                <ExternalLink className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Categories</p>
                <p className="text-2xl font-bold">
                  {new Set(projectsData?.projects?.map(p => p.category)).size || 0}
                </p>
              </div>
              <div className="w-8 h-8 bg-blue-500/10 rounded-lg flex items-center justify-center">
                <Calendar className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </AdminLayout>
  )
}
