/**
 * Admin Login Page
 * 
 * Secure login interface for admin users with rate limiting,
 * proper validation, and accessibility features.
 */

'use client'

import { useState, useEffect } from 'react'
import { signIn, getSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { Eye, EyeOff, Lock, Mail, AlertCircle, Shield } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { AdminLoginCredentials } from '@/types/admin'

/**
 * Form validation schema
 */
const validateForm = (credentials: AdminLoginCredentials): Record<string, string> => {
  const errors: Record<string, string> = {}

  if (!credentials.email) {
    errors.email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(credentials.email)) {
    errors.email = 'Please enter a valid email address'
  }

  if (!credentials.password) {
    errors.password = 'Password is required'
  } else if (credentials.password.length < 6) {
    errors.password = 'Password must be at least 6 characters'
  }

  return errors
}

/**
 * Admin Login Page Component
 */
export default function AdminLoginPage(): JSX.Element {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [credentials, setCredentials] = useState<AdminLoginCredentials>({
    email: '',
    password: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [attemptCount, setAttemptCount] = useState(0)
  const [isLocked, setIsLocked] = useState(false)
  const [lockoutTime, setLockoutTime] = useState<number | null>(null)

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      const session = await getSession()
      if (session?.user?.role === 'admin') {
        router.replace('/admin/dashboard')
      }
    }
    checkSession()
  }, [router])

  // Handle lockout countdown
  useEffect(() => {
    if (lockoutTime && lockoutTime > Date.now()) {
      const interval = setInterval(() => {
        if (Date.now() >= lockoutTime) {
          setIsLocked(false)
          setLockoutTime(null)
          setAttemptCount(0)
          clearInterval(interval)
        }
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [lockoutTime])

  /**
   * Handles input changes with real-time validation
   */
  const handleInputChange = (field: keyof AdminLoginCredentials) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value
    setCredentials(prev => ({ ...prev, [field]: value }))
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  /**
   * Handles form submission with security measures
   */
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    // Check if locked out
    if (isLocked) {
      toast.error('Too many failed attempts. Please wait before trying again.')
      return
    }

    // Validate form
    const formErrors = validateForm(credentials)
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors)
      return
    }

    setIsLoading(true)
    setErrors({})

    try {
      const result = await signIn('credentials', {
        email: credentials.email,
        password: credentials.password,
        redirect: false,
      })

      if (result?.error) {
        const newAttemptCount = attemptCount + 1
        setAttemptCount(newAttemptCount)

        // Implement progressive lockout
        if (newAttemptCount >= 5) {
          setIsLocked(true)
          setLockoutTime(Date.now() + 15 * 60 * 1000) // 15 minutes
          toast.error('Too many failed attempts. Account locked for 15 minutes.')
        } else {
          toast.error(`Invalid credentials. ${5 - newAttemptCount} attempts remaining.`)
        }
      } else if (result?.ok) {
        // Verify admin role
        const session = await getSession()
        if (session?.user?.role === 'admin') {
          toast.success('Welcome back, Administrator!')
          const callbackUrl = searchParams.get('callbackUrl') || '/admin/dashboard'
          router.replace(callbackUrl)
        } else {
          toast.error('Access denied. Admin privileges required.')
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      toast.error('Login failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Calculates remaining lockout time in minutes and seconds
   */
  const getRemainingLockoutTime = (): string => {
    if (!lockoutTime) return ''
    
    const remaining = Math.max(0, lockoutTime - Date.now())
    const minutes = Math.floor(remaining / 60000)
    const seconds = Math.floor((remaining % 60000) / 1000)
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-2xl border-0">
          <CardHeader className="text-center pb-8">
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mx-auto w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-4"
            >
              <Shield className="h-8 w-8 text-primary-foreground" />
            </motion.div>
            <CardTitle className="text-2xl font-bold">Admin Portal</CardTitle>
            <p className="text-muted-foreground">
              Sign in to access the administration dashboard
            </p>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6" noValidate>
              {/* Email Field */}
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={credentials.email}
                    onChange={handleInputChange('email')}
                    className={`pl-10 ${errors.email ? 'border-destructive' : ''}`}
                    disabled={isLoading || isLocked}
                    autoComplete="email"
                    aria-describedby={errors.email ? 'email-error' : undefined}
                  />
                </div>
                {errors.email && (
                  <p id="email-error" className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.email}
                  </p>
                )}
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    value={credentials.password}
                    onChange={handleInputChange('password')}
                    className={`pl-10 pr-10 ${errors.password ? 'border-destructive' : ''}`}
                    disabled={isLoading || isLocked}
                    autoComplete="current-password"
                    aria-describedby={errors.password ? 'password-error' : undefined}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                    disabled={isLoading || isLocked}
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.password && (
                  <p id="password-error" className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.password}
                  </p>
                )}
              </div>

              {/* Lockout Warning */}
              {isLocked && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="p-3 bg-destructive/10 border border-destructive/20 rounded-md"
                >
                  <p className="text-sm text-destructive flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    Account locked. Try again in {getRemainingLockoutTime()}
                  </p>
                </motion.div>
              )}

              {/* Attempt Counter */}
              {attemptCount > 0 && attemptCount < 5 && !isLocked && (
                <p className="text-sm text-muted-foreground text-center">
                  {5 - attemptCount} attempts remaining
                </p>
              )}

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading || isLocked}
                size="lg"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>

            {/* Security Notice */}
            <div className="mt-6 p-3 bg-muted/50 rounded-md">
              <p className="text-xs text-muted-foreground text-center">
                This is a secure admin area. All login attempts are monitored and logged.
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
