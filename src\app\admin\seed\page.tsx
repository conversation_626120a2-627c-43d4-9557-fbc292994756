/**
 * Database Seeding Page
 * 
 * Admin page for seeding the database with real portfolio data.
 */

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Database, AlertTriangle, CheckCircle, Loader2 } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useApiMutation } from '@/hooks/use-api'

export default function SeedPage(): JSX.Element {
  const [seedResult, setSeedResult] = useState<any>(null)
  const { mutate: seedDatabase, loading } = useApiMutation()

  const handleSeed = async () => {
    try {
      const result = await seedDatabase('/api/admin/seed', {
        seedKey: 'seed-portfolio-2024'
      })
      
      setSeedResult(result)
      toast.success('Database seeded successfully!')
    } catch (error) {
      toast.error('Failed to seed database')
      console.error('Seeding error:', error)
    }
  }

  return (
    <AdminLayout 
      title="Database Seeding"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Database Seeding' },
      ]}
    >
      <div className="max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-8"
        >
          {/* Warning Alert */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Warning:</strong> This will replace all existing data in the database with real portfolio content. 
              This action cannot be undone. Only proceed if you want to populate the database with authentic data.
            </AlertDescription>
          </Alert>

          {/* Seeding Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Seed Portfolio Database
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">What will be seeded:</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">📁 Projects (6 items)</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Smooth Beauty - Beauty marketplace</li>
                      <li>• Crowned Naturally - E-commerce platform</li>
                      <li>• Shoppoint - Multi-vendor marketplace</li>
                      <li>• Portfolio Website - This website</li>
                      <li>• Task Management API - RESTful API</li>
                      <li>• Weather Dashboard - React app</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">📧 Contacts (5 items)</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Job opportunities</li>
                      <li>• Project collaborations</li>
                      <li>• Technical consultations</li>
                      <li>• Partnership inquiries</li>
                      <li>• Recruitment messages</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">📰 Newsletter (5 subscribers)</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Software engineering enthusiasts</li>
                      <li>• React/Node.js developers</li>
                      <li>• Mobile development followers</li>
                      <li>• Frontend/backend specialists</li>
                      <li>• Tech community members</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">📝 Blog Posts (3 items)</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• E-commerce platform architecture</li>
                      <li>• React Native vs Flutter comparison</li>
                      <li>• TypeScript best practices (draft)</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="border-t pt-6">
                <Button 
                  onClick={handleSeed}
                  disabled={loading}
                  size="lg"
                  className="w-full md:w-auto"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Seeding Database...
                    </>
                  ) : (
                    <>
                      <Database className="h-4 w-4 mr-2" />
                      Seed Database with Real Data
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Results */}
          {seedResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-600">
                    <CheckCircle className="h-5 w-5" />
                    Seeding Completed Successfully
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {seedResult.data?.projects || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">Projects</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {seedResult.data?.contacts || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">Contacts</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {seedResult.data?.newsletters || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">Subscribers</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {seedResult.data?.blogPosts || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">Blog Posts</div>
                    </div>
                  </div>
                  
                  <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300">
                      ✅ Database has been successfully populated with real portfolio data. 
                      You can now test all CRUD operations and see authentic content throughout the application.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle>Next Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <p>After seeding the database, you can:</p>
                <ul className="space-y-2 ml-4">
                  <li>• Visit the <strong>Projects</strong> page to see real project data</li>
                  <li>• Check the <strong>Dashboard</strong> for updated statistics</li>
                  <li>• Test CRUD operations on all seeded content</li>
                  <li>• View the main portfolio pages with real data</li>
                  <li>• Manage contacts and newsletter subscribers</li>
                  <li>• Edit or publish blog posts</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AdminLayout>
  )
}
