import mongoose, { Schema } from 'mongoose'
import { IEducation } from '@/types'

const EducationSchema = new Schema<IEducation>(
  {
    degree: {
      type: String,
      required: [true, 'Degree is required'],
      trim: true,
      maxlength: [200, 'Degree cannot exceed 200 characters'],
    },
    institution: {
      type: String,
      required: [true, 'Institution is required'],
      trim: true,
      maxlength: [200, 'Institution cannot exceed 200 characters'],
    },
    location: {
      type: String,
      required: [true, 'Location is required'],
      trim: true,
      maxlength: [100, 'Location cannot exceed 100 characters'],
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required'],
    },
    endDate: {
      type: Date,
      validate: {
        validator: function (this: IEducation, value: Date) {
          return !value || value > this.startDate
        },
        message: 'End date must be after start date',
      },
    },
    current: {
      type: Boolean,
      default: false,
    },
    description: {
      type: String,
      trim: true,
      maxlength: [1000, 'Description cannot exceed 1000 characters'],
    },
    order: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
)

// Index for ordering
EducationSchema.index({ order: 1, startDate: -1 })

export default mongoose.models.Education || mongoose.model<IEducation>('Education', EducationSchema)
