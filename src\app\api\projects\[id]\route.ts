import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/nextauth'
import connectDB from '@/lib/mongodb'
import { Project } from '@/lib/models'
import { ApiResponse } from '@/types'
import { getZodErrorMessage } from '@/utils'

// Validation schema
const projectSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title cannot exceed 100 characters'),
  description: z.string().min(1, 'Description is required').max(500, 'Description cannot exceed 500 characters'),
  longDescription: z.string().max(2000, 'Long description cannot exceed 2000 characters').optional(),
  technologies: z.array(z.string()).min(1, 'At least one technology is required'),
  category: z.enum(['web', 'mobile', 'wordpress', 'other']),
  featured: z.boolean().default(false),
  images: z.array(z.string()).default([]),
  liveUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  githubUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  demoUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  order: z.number().default(0),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()
    
    const project = await Project.findById(params.id).lean()
    
    if (!project) {
      const response: ApiResponse = {
        success: false,
        error: 'Project not found',
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      data: project,
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Get project error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch project',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user?.role !== 'admin') {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication required. Admin access only.',
      }
      return NextResponse.json(response, { status: 401 })
    }
    
    const body = await request.json()
    
    // Validate input
    const validatedData = projectSchema.parse(body)
    
    // Connect to database
    await connectDB()
    
    // Update project
    const project = await Project.findByIdAndUpdate(
      params.id,
      validatedData,
      { new: true, runValidators: true }
    )
    
    if (!project) {
      const response: ApiResponse = {
        success: false,
        error: 'Project not found',
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      data: project,
      message: 'Project updated successfully',
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Update project error:', error)
    
    if (error instanceof z.ZodError) {
      const response: ApiResponse = {
        success: false,
        error: getZodErrorMessage(error),
      }
      return NextResponse.json(response, { status: 400 })
    }
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to update project',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user?.role !== 'admin') {
      const response: ApiResponse = {
        success: false,
        error: 'Authentication required. Admin access only.',
      }
      return NextResponse.json(response, { status: 401 })
    }
    
    // Connect to database
    await connectDB()
    
    // Delete project
    const project = await Project.findByIdAndDelete(params.id)
    
    if (!project) {
      const response: ApiResponse = {
        success: false,
        error: 'Project not found',
      }
      return NextResponse.json(response, { status: 404 })
    }
    
    const response: ApiResponse = {
      success: true,
      message: 'Project deleted successfully',
    }
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Delete project error:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to delete project',
    }
    
    return NextResponse.json(response, { status: 500 })
  }
}
