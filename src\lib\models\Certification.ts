import mongoose, { Schema } from 'mongoose'
import { ICertification } from '@/types'

const CertificationSchema = new Schema<ICertification>(
  {
    name: {
      type: String,
      required: [true, 'Certification name is required'],
      trim: true,
      maxlength: [200, 'Certification name cannot exceed 200 characters'],
    },
    issuer: {
      type: String,
      required: [true, 'Issuer is required'],
      trim: true,
      maxlength: [100, 'Issuer cannot exceed 100 characters'],
    },
    issueDate: {
      type: Date,
      required: [true, 'Issue date is required'],
    },
    expiryDate: {
      type: Date,
      validate: {
        validator: function (this: ICertification, value: Date) {
          return !value || value > this.issueDate
        },
        message: 'Expiry date must be after issue date',
      },
    },
    credentialId: {
      type: String,
      trim: true,
      maxlength: [100, 'Credential ID cannot exceed 100 characters'],
    },
    credentialUrl: {
      type: String,
      trim: true,
      validate: {
        validator: function (value: string) {
          if (!value) return true
          return /^https?:\/\/.+/.test(value)
        },
        message: 'Please enter a valid URL',
      },
    },
    order: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
)

// Index for ordering
CertificationSchema.index({ order: 1, issueDate: -1 })

export default mongoose.models.Certification || mongoose.model<ICertification>('Certification', CertificationSchema)
