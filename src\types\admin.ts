/**
 * Admin Dashboard Type Definitions
 * 
 * This file contains comprehensive type definitions for the admin dashboard,
 * including authentication, data management, and UI state types.
 */

import type { IUser, IProject, IBlogPost, IContact, INewsletter } from './index'

/**
 * Admin authentication and session types
 */
export interface AdminSession {
  user: {
    id: string
    email: string
    name: string
    role: 'admin'
  }
  expires: string
}

export interface AdminLoginCredentials {
  email: string
  password: string
}

export interface AdminLoginResponse {
  success: boolean
  user?: AdminSession['user']
  token?: string
  message?: string
  error?: string
}

/**
 * Dashboard statistics and analytics types
 */
export interface DashboardMetrics {
  totalProjects: number
  totalBlogPosts: number
  totalContacts: number
  totalNewsletterSubscribers: number
  publishedBlogPosts: number
  draftBlogPosts: number
  unreadContacts: number
  activeSubscribers: number
}

export interface DashboardChartData {
  labels: string[]
  datasets: Array<{
    label: string
    data: number[]
    backgroundColor?: string
    borderColor?: string
    borderWidth?: number
  }>
}

export interface RecentActivity {
  id: string
  type: 'contact' | 'newsletter' | 'blog' | 'project'
  title: string
  description: string
  timestamp: Date
  status: 'new' | 'updated' | 'published' | 'archived'
}

/**
 * Data management types for CRUD operations
 */
export interface DataTableColumn<T = unknown> {
  key: keyof T
  label: string
  sortable?: boolean
  filterable?: boolean
  render?: (value: unknown, row: T) => React.ReactNode
  width?: string
}

export interface DataTableProps<T = unknown> {
  data: T[]
  columns: DataTableColumn<T>[]
  loading?: boolean
  error?: string | null
  pagination?: {
    page: number
    limit: number
    total: number
    pages: number
  }
  onPageChange?: (page: number) => void
  onSort?: (column: keyof T, direction: 'asc' | 'desc') => void
  onFilter?: (filters: Record<string, unknown>) => void
  onRowClick?: (row: T) => void
  onRowSelect?: (selectedRows: T[]) => void
  actions?: Array<{
    label: string
    icon?: React.ComponentType<{ className?: string }>
    onClick: (row: T) => void
    variant?: 'default' | 'destructive' | 'outline'
    disabled?: (row: T) => boolean
  }>
}

/**
 * Form management types
 */
export interface FormField<T = unknown> {
  name: keyof T
  label: string
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'date' | 'file' | 'rich-text'
  required?: boolean
  placeholder?: string
  options?: Array<{ value: string; label: string }>
  validation?: {
    min?: number
    max?: number
    pattern?: RegExp
    custom?: (value: unknown) => string | null
  }
  disabled?: boolean
  hidden?: boolean
}

export interface FormProps<T = Record<string, unknown>> {
  fields: FormField<T>[]
  initialData?: Partial<T>
  onSubmit: (data: T) => Promise<void>
  onCancel?: () => void
  loading?: boolean
  submitLabel?: string
  cancelLabel?: string
}

/**
 * Modal and dialog types
 */
export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closeOnOverlayClick?: boolean
  showCloseButton?: boolean
}

export interface ConfirmDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description: string
  confirmLabel?: string
  cancelLabel?: string
  variant?: 'default' | 'destructive'
  loading?: boolean
}

/**
 * Navigation and routing types
 */
export interface AdminNavItem {
  id: string
  label: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
  children?: AdminNavItem[]
  permissions?: string[]
}

export interface BreadcrumbItem {
  label: string
  href?: string
}

/**
 * Content management types
 */
export interface ContentEditor {
  content: string
  onChange: (content: string) => void
  placeholder?: string
  toolbar?: boolean
  readOnly?: boolean
  maxLength?: number
}

export interface MediaUpload {
  file: File
  preview?: string
  progress?: number
  error?: string
  uploaded?: boolean
  url?: string
}

export interface MediaLibraryItem {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  thumbnailUrl?: string
  uploadedAt: Date
  uploadedBy: string
  alt?: string
  caption?: string
}

/**
 * Settings and configuration types
 */
export interface AdminSettings {
  siteName: string
  siteDescription: string
  siteUrl: string
  adminEmail: string
  maintenanceMode: boolean
  allowRegistration: boolean
  emailNotifications: boolean
  backupFrequency: 'daily' | 'weekly' | 'monthly'
  timezone: string
  dateFormat: string
  theme: 'light' | 'dark' | 'system'
}

/**
 * API response types for admin endpoints
 */
export interface AdminApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  error?: string
  meta?: {
    pagination?: {
      page: number
      limit: number
      total: number
      pages: number
    }
    filters?: Record<string, unknown>
    sort?: {
      field: string
      direction: 'asc' | 'desc'
    }
  }
}

/**
 * Error handling types
 */
export interface AdminError {
  code: string
  message: string
  details?: Record<string, unknown>
  timestamp: Date
  userId?: string
  action?: string
}

export interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

/**
 * Utility types for admin operations
 */
export type AdminAction = 'create' | 'read' | 'update' | 'delete' | 'publish' | 'archive'

export type AdminResource = 'projects' | 'blog-posts' | 'contacts' | 'newsletter' | 'users' | 'settings'

export interface AdminPermission {
  resource: AdminResource
  actions: AdminAction[]
}

export interface AdminRole {
  id: string
  name: string
  description: string
  permissions: AdminPermission[]
}

/**
 * Type guards for runtime type checking
 */
export const isAdminSession = (obj: unknown): obj is AdminSession => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'user' in obj &&
    'expires' in obj &&
    typeof (obj as AdminSession).user.role === 'string' &&
    (obj as AdminSession).user.role === 'admin'
  )
}

export const isAdminApiResponse = <T>(obj: unknown): obj is AdminApiResponse<T> => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'success' in obj &&
    typeof (obj as AdminApiResponse).success === 'boolean'
  )
}
