/**
 * Admin Setup Checker Script
 * 
 * This script checks if an admin user exists and provides setup instructions.
 * Run with: node scripts/check-admin.js
 */

const { MongoClient } = require('mongodb')
require('dotenv').config({ path: '.env.local' })

async function checkAdminSetup() {
  const mongoUri = process.env.MONGODB_URI
  
  if (!mongoUri) {
    console.error('❌ MONGODB_URI not found in environment variables')
    console.log('Please check your .env.local file')
    process.exit(1)
  }

  let client

  try {
    console.log('🔍 Checking admin setup...\n')
    
    // Connect to MongoDB
    client = new MongoClient(mongoUri)
    await client.connect()
    
    const db = client.db()
    const users = db.collection('users')
    
    // Check for admin user
    const adminUser = await users.findOne({ role: 'admin' })
    
    if (adminUser) {
      console.log('✅ Admin user found!')
      console.log(`📧 Email: ${adminUser.email}`)
      console.log(`👤 Name: ${adminUser.name}`)
      console.log(`📅 Created: ${adminUser.createdAt || 'Unknown'}`)
      console.log('\n🚀 You can login at: http://localhost:3000/admin/login')
    } else {
      console.log('❌ No admin user found')
      console.log('\n🛠️  Setup Instructions:')
      console.log('1. Start your development server: npm run dev')
      console.log('2. Visit: http://localhost:3000/admin/setup')
      console.log('3. Use setup key: admin-setup-2024')
      console.log('4. Create your admin account')
      console.log('\n📋 Suggested credentials for testing:')
      console.log('   Email: <EMAIL>')
      console.log('   Password: Admin123!')
      console.log('   Setup Key: admin-setup-2024')
    }
    
    // Check total users
    const totalUsers = await users.countDocuments()
    console.log(`\n📊 Total users in database: ${totalUsers}`)
    
  } catch (error) {
    console.error('❌ Error checking admin setup:', error.message)
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Troubleshooting:')
      console.log('- Make sure MongoDB is running')
      console.log('- Check your MONGODB_URI in .env.local')
      console.log('- Verify MongoDB connection settings')
    }
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// Run the check
checkAdminSetup().catch(console.error)
