'use client'

import { motion } from 'framer-motion'
import { Calendar, MapPin, ExternalLink } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatDate } from '@/utils'

// Sample experience data based on your resume
const experiences = [
  {
    id: '1',
    title: 'Software Engineering Instructor',
    company: 'Aptech',
    location: 'Hybrid',
    startDate: new Date('2024-11-01'),
    endDate: new Date('2025-06-01'),
    current: false,
    description: [
      'Delivered over 1,300 hours of full-stack training to nearly 200 students, covering HTML, CSS, JavaScript, React, .NET Core, MongoDB, C#, and Azure',
      'Led students through over 10 portfolio-grade projects, with 80% of students becoming job-ready and actively interviewing within 6 months',
      'Focused on building and deploying real-world applications with industry best practices'
    ],
    technologies: ['HTML', 'CSS', 'JavaScript', 'React', '.NET Core', 'MongoDB', 'C#', 'Azure']
  },
  {
    id: '2',
    title: 'Freelance Software Engineer',
    company: 'Startup & Small Business Projects',
    location: 'Remote',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-10-01'),
    current: false,
    description: [
      'Delivered web/mobile solutions (React, Node.js, MongoDB) for clients in the US, UK, Australia, China, Brazil',
      'Delivered 3 MVPs in 4 weeks - helping a startup gain its first customers',
      'Integrated CI/CD pipelines and async collaboration to ensure smooth delivery'
    ],
    technologies: ['React', 'Node.js', 'MongoDB', 'CI/CD', 'MVP Development']
  },
  {
    id: '3',
    title: 'Associate Fullstack Engineer',
    company: 'Ceedlab',
    location: 'Remote',
    startDate: new Date('2023-06-01'),
    endDate: new Date('2023-12-01'),
    current: false,
    description: [
      'Built backend microservices with Node.js and PHP, improving response times by 20% and simplifying future updates',
      'Shipped cross-platform UI features with React, Vue, and Next.js, and compiled to mobile using React Native and Ionic',
      'Set up CI/CD with Bitbucket and Jira for faster releases and smoother handoffs between development, QA, and design teams',
      'Shipped clean, tested code in Agile sprints, cutting bug reports by 30% and speeding up releases'
    ],
    technologies: ['Node.js', 'PHP', 'React', 'Vue.js', 'Next.js', 'React Native', 'Ionic', 'CI/CD', 'Agile']
  },
  {
    id: '4',
    title: 'JavaScript Instructor (Fullstack)',
    company: 'SQI College of ICT',
    location: 'Hybrid',
    startDate: new Date('2021-10-01'),
    endDate: new Date('2023-03-01'),
    current: false,
    description: [
      'Progressed from advanced student to peer mentor, taking on instructional duties showing technical growth and leadership',
      'Delivered a fullstack curriculum covering HTML, CSS, JavaScript, React, Node.js, and server administration achieving 90% course completion and a 4.8/5 student rating',
      'Taught version control (Git), server-side rendering, and backend concepts, boosting learner confidence and project outcomes',
      'Supported over 80% of students in launching real-world projects to production environments'
    ],
    technologies: ['HTML', 'CSS', 'JavaScript', 'React', 'Node.js', 'Git', 'Server Administration']
  },
  {
    id: '5',
    title: 'Co-Founder',
    company: 'Dream Intrepid',
    location: 'Remote',
    startDate: new Date('2019-03-01'),
    endDate: new Date('2023-12-01'),
    current: false,
    description: [
      'Designed and deployed frontend solutions for 10+ e-commerce websites, improving load speed, user flow, and visual consistency',
      'Built and optimized frontend performance for 20+ client sites, increasing performance by 61%',
      'Built and maintained WordPress sites with custom themes and plugins, improving SEO and user experience',
      'Managed UI/UX assets and content updates across 10+ pages, consistently driving organic traffic with high-quality visuals',
      'Reduced project turnaround time by 43% through well-documented workflows and reusable design components'
    ],
    technologies: ['WordPress', 'Frontend Development', 'SEO', 'UI/UX', 'Performance Optimization']
  }
]

interface ExperienceSectionProps {
  showAll?: boolean
  limit?: number
}

export function ExperienceSection({ showAll = true, limit = 3 }: ExperienceSectionProps) {
  const displayedExperiences = showAll ? experiences : experiences.slice(0, limit)

  return (
    <div className="space-y-6">
      {displayedExperiences.map((experience, index) => (
        <motion.div
          key={experience.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
        >
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <CardTitle className="text-xl">{experience.title}</CardTitle>
                  <div className="flex items-center gap-2 text-primary font-medium">
                    <span>{experience.company}</span>
                    {experience.company !== experience.location && (
                      <>
                        <span>•</span>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          <span>{experience.location}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {formatDate(experience.startDate, { month: 'short', year: 'numeric' })} - {' '}
                    {experience.current 
                      ? 'Present' 
                      : formatDate(experience.endDate!, { month: 'short', year: 'numeric' })
                    }
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <ul className="space-y-2">
                {experience.description.map((item, idx) => (
                  <li key={idx} className="text-muted-foreground flex items-start gap-2">
                    <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
              
              {experience.technologies.length > 0 && (
                <div className="pt-4 border-t">
                  <div className="flex flex-wrap gap-2">
                    {experience.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-2 py-1 bg-muted text-muted-foreground rounded text-xs hover:bg-primary hover:text-primary-foreground transition-colors"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}
