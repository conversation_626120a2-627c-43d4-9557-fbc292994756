import mongoose, { Schema } from 'mongoose'
import { IProject } from '@/types'

const ProjectSchema = new Schema<IProject>(
  {
    title: {
      type: String,
      required: [true, 'Project title is required'],
      trim: true,
      maxlength: [100, 'Title cannot exceed 100 characters'],
    },
    description: {
      type: String,
      required: [true, 'Project description is required'],
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },
    longDescription: {
      type: String,
      trim: true,
      maxlength: [2000, 'Long description cannot exceed 2000 characters'],
    },
    technologies: {
      type: [String],
      required: [true, 'At least one technology is required'],
      validate: {
        validator: function (value: string[]) {
          return value.length > 0
        },
        message: 'At least one technology is required',
      },
    },
    category: {
      type: String,
      enum: ['web', 'mobile', 'wordpress', 'other'],
      required: [true, 'Category is required'],
    },
    featured: {
      type: Boolean,
      default: false,
    },
    images: {
      type: [String],
      default: [],
    },
    liveUrl: {
      type: String,
      trim: true,
      validate: {
        validator: function (value: string) {
          if (!value) return true
          return /^https?:\/\/.+/.test(value)
        },
        message: 'Please enter a valid URL',
      },
    },
    githubUrl: {
      type: String,
      trim: true,
      validate: {
        validator: function (value: string) {
          if (!value) return true
          return /^https?:\/\/.+/.test(value)
        },
        message: 'Please enter a valid URL',
      },
    },
    demoUrl: {
      type: String,
      trim: true,
      validate: {
        validator: function (value: string) {
          if (!value) return true
          return /^https?:\/\/.+/.test(value)
        },
        message: 'Please enter a valid URL',
      },
    },
    order: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
)

// Index for ordering and filtering
ProjectSchema.index({ order: 1, createdAt: -1 })
ProjectSchema.index({ category: 1, featured: -1 })
ProjectSchema.index({ featured: -1, order: 1 })

export default mongoose.models.Project || mongoose.model<IProject>('Project', ProjectSchema)
