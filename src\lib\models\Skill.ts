import mongoose, { Schema } from 'mongoose'
import { ISkill } from '@/types'

const SkillSchema = new Schema<ISkill>(
  {
    name: {
      type: String,
      required: [true, 'Skill name is required'],
      trim: true,
      maxlength: [100, 'Skill name cannot exceed 100 characters'],
    },
    category: {
      type: String,
      enum: ['frontend', 'backend', 'database', 'devops', 'tools', 'soft'],
      required: [true, 'Category is required'],
    },
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'expert'],
      required: [true, 'Level is required'],
    },
    order: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
)

// Indexes for ordering and filtering
SkillSchema.index({ category: 1, order: 1 })
SkillSchema.index({ level: 1, category: 1 })

export default mongoose.models.Skill || mongoose.model<ISkill>('Skill', SkillSchema)
