{"name": "folio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@next-auth/mongodb-adapter": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "gsap": "^3.13.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.536.0", "mongodb": "^5.9.2", "mongoose": "^8.17.0", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}