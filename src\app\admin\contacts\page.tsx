/**
 * Admin Contacts Management Page
 * 
 * Provides comprehensive contact message management including viewing,
 * filtering, status updates, and bulk operations.
 */

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Mail,
  MailOpen,
  Reply,
  Archive,
  Trash2,
  Filter,
  Search,
  Calendar,
  User,
  MessageSquare,
  ExternalLink,
  MoreHorizontal,
  CheckCircle,
  Clock,
  AlertCircle,
} from 'lucide-react'
import { format } from 'date-fns'
import { AdminLayout } from '@/components/admin/admin-layout'
import { DataTable } from '@/components/admin/data-table'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { useApi, useApiMutation } from '@/hooks/use-api'
import { toast } from 'react-hot-toast'
import type { IContact } from '@/types'

interface ContactTableData extends IContact {
  _id: string
  name: string
  email: string
  subject: string
  message: string
  status: 'new' | 'read' | 'replied' | 'archived'
  createdAt: Date
  updatedAt: Date
}

/**
 * Admin Contacts Page Component
 */
export default function AdminContactsPage(): JSX.Element {
  const [selectedContacts, setSelectedContacts] = useState<ContactTableData[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'new' | 'read' | 'replied' | 'archived'>('all')
  const [selectedContact, setSelectedContact] = useState<ContactTableData | null>(null)
  
  // API hooks
  const { data: contactsData, loading, error, refetch } = useApi<{
    contacts: ContactTableData[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }>('/api/contact')
  
  const { mutate: updateContactStatus, loading: updating } = useApiMutation()
  const { mutate: deleteContact, loading: deleting } = useApiMutation()

  // Handle row selection
  const handleRowSelect = (contacts: ContactTableData[]) => {
    setSelectedContacts(contacts)
  }

  // Handle status update
  const handleStatusUpdate = async (contactId: string, status: ContactTableData['status']) => {
    try {
      await updateContactStatus(`/api/contact/${contactId}`, { status }, 'PUT')
      toast.success('Contact status updated successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to update contact status')
    }
  }

  // Handle bulk status update
  const handleBulkStatusUpdate = async (status: ContactTableData['status']) => {
    try {
      await Promise.all(
        selectedContacts.map(contact => 
          updateContactStatus(`/api/contact/${contact._id}`, { status }, 'PUT')
        )
      )
      toast.success(`${selectedContacts.length} contacts updated successfully`)
      setSelectedContacts([])
      refetch()
    } catch (error) {
      toast.error('Failed to update some contacts')
    }
  }

  // Handle single contact deletion
  const handleDeleteContact = async (contactId: string) => {
    if (!confirm('Are you sure you want to delete this contact? This action cannot be undone.')) {
      return
    }

    try {
      await deleteContact(`/api/contact/${contactId}`, {}, 'DELETE')
      toast.success('Contact deleted successfully')
      refetch()
    } catch (error) {
      toast.error('Failed to delete contact')
    }
  }

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedContacts.length} contacts? This action cannot be undone.`)) {
      return
    }

    try {
      await Promise.all(
        selectedContacts.map(contact => 
          deleteContact(`/api/contact/${contact._id}`, {}, 'DELETE')
        )
      )
      toast.success(`${selectedContacts.length} contacts deleted successfully`)
      setSelectedContacts([])
      refetch()
    } catch (error) {
      toast.error('Failed to delete some contacts')
    }
  }

  // Get status badge variant
  const getStatusBadgeVariant = (status: ContactTableData['status']) => {
    switch (status) {
      case 'new': return 'destructive'
      case 'read': return 'secondary'
      case 'replied': return 'default'
      case 'archived': return 'outline'
      default: return 'secondary'
    }
  }

  // Get status icon
  const getStatusIcon = (status: ContactTableData['status']) => {
    switch (status) {
      case 'new': return AlertCircle
      case 'read': return MailOpen
      case 'replied': return CheckCircle
      case 'archived': return Archive
      default: return Mail
    }
  }

  // Table columns configuration
  const columns = [
    {
      key: 'name',
      label: 'Contact',
      render: (contact: ContactTableData) => (
        <div className="space-y-1">
          <div className="font-medium">{contact.name}</div>
          <div className="text-sm text-muted-foreground">{contact.email}</div>
        </div>
      ),
    },
    {
      key: 'subject',
      label: 'Subject',
      render: (contact: ContactTableData) => (
        <div className="max-w-xs">
          <div className="font-medium truncate">{contact.subject}</div>
          <div className="text-sm text-muted-foreground line-clamp-2">
            {contact.message.substring(0, 100)}...
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (contact: ContactTableData) => {
        const StatusIcon = getStatusIcon(contact.status)
        return (
          <Badge variant={getStatusBadgeVariant(contact.status)} className="gap-1">
            <StatusIcon className="h-3 w-3" />
            {contact.status.charAt(0).toUpperCase() + contact.status.slice(1)}
          </Badge>
        )
      },
    },
    {
      key: 'createdAt',
      label: 'Received',
      render: (contact: ContactTableData) => (
        <div className="text-sm">
          <div>{format(new Date(contact.createdAt), 'MMM dd, yyyy')}</div>
          <div className="text-muted-foreground">{format(new Date(contact.createdAt), 'HH:mm')}</div>
        </div>
      ),
    },
  ]

  // Table actions configuration
  const actions = [
    {
      label: 'View Details',
      icon: MessageSquare,
      onClick: (contact: ContactTableData) => setSelectedContact(contact),
    },
    {
      label: 'Mark as Read',
      icon: MailOpen,
      onClick: (contact: ContactTableData) => handleStatusUpdate(contact._id, 'read'),
      show: (contact: ContactTableData) => contact.status === 'new',
    },
    {
      label: 'Mark as Replied',
      icon: Reply,
      onClick: (contact: ContactTableData) => handleStatusUpdate(contact._id, 'replied'),
      show: (contact: ContactTableData) => contact.status !== 'replied',
    },
    {
      label: 'Archive',
      icon: Archive,
      onClick: (contact: ContactTableData) => handleStatusUpdate(contact._id, 'archived'),
      show: (contact: ContactTableData) => contact.status !== 'archived',
    },
    {
      label: 'Reply via Email',
      icon: ExternalLink,
      onClick: (contact: ContactTableData) => {
        window.open(`mailto:${contact.email}?subject=Re: ${contact.subject}`, '_blank')
        handleStatusUpdate(contact._id, 'replied')
      },
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (contact: ContactTableData) => handleDeleteContact(contact._id),
      variant: 'destructive' as const,
    },
  ]

  // Filter contacts based on search and status
  const filteredContacts = contactsData?.contacts?.filter(contact => {
    const matchesSearch = !searchQuery || 
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.message.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || contact.status === statusFilter
    
    return matchesSearch && matchesStatus
  }) || []

  return (
    <AdminLayout
      title="Contact Messages"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Contacts' },
      ]}
    >
      <div className="space-y-6">
        {/* Header with Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid gap-4 md:grid-cols-4"
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Messages</CardTitle>
              <Mail className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{contactsData?.contacts?.length || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Messages</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {contactsData?.contacts?.filter(c => c.status === 'new').length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Replied</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {contactsData?.contacts?.filter(c => c.status === 'replied').length || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Archived</CardTitle>
              <Archive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {contactsData?.contacts?.filter(c => c.status === 'archived').length || 0}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Header Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex items-center justify-between"
        >
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Contact Messages</h2>
            <p className="text-muted-foreground">
              Manage and respond to contact form submissions
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {selectedContacts.length > 0 && (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">
                      <MoreHorizontal className="h-4 w-4 mr-2" />
                      Bulk Actions ({selectedContacts.length})
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleBulkStatusUpdate('read')}>
                      <MailOpen className="h-4 w-4 mr-2" />
                      Mark as Read
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkStatusUpdate('replied')}>
                      <Reply className="h-4 w-4 mr-2" />
                      Mark as Replied
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkStatusUpdate('archived')}>
                      <Archive className="h-4 w-4 mr-2" />
                      Archive
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={handleBulkDelete}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex items-center space-x-4"
        >
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search contacts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Status: {statusFilter === 'all' ? 'All' : statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                All Messages
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('new')}>
                New
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('read')}>
                Read
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('replied')}>
                Replied
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('archived')}>
                Archived
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </motion.div>

        {/* Contacts Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <DataTable
            data={filteredContacts}
            columns={columns}
            loading={loading}
            error={error}
            pagination={contactsData?.pagination}
            actions={actions}
            onRowSelect={handleRowSelect}
            onRowClick={(contact) => setSelectedContact(contact)}
          />
        </motion.div>
      </div>

      {/* Contact Detail Modal */}
      {selectedContact && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-card border rounded-lg shadow-lg max-w-2xl w-full max-h-[80vh] overflow-hidden"
          >
            <div className="flex items-center justify-between p-6 border-b">
              <div>
                <h3 className="text-lg font-semibold">{selectedContact.subject}</h3>
                <p className="text-sm text-muted-foreground">
                  From {selectedContact.name} • {format(new Date(selectedContact.createdAt), 'MMM dd, yyyy at HH:mm')}
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSelectedContact(null)}
              >
                ×
              </Button>
            </div>

            <div className="p-6 space-y-4 overflow-y-auto">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span className="font-medium">{selectedContact.name}</span>
                  <span className="text-muted-foreground">({selectedContact.email})</span>
                </div>
                <Badge variant={getStatusBadgeVariant(selectedContact.status)}>
                  {selectedContact.status.charAt(0).toUpperCase() + selectedContact.status.slice(1)}
                </Badge>
              </div>

              <div className="bg-muted/50 rounded-lg p-4">
                <p className="whitespace-pre-wrap">{selectedContact.message}</p>
              </div>

              <div className="flex items-center justify-end space-x-2 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => {
                    window.open(`mailto:${selectedContact.email}?subject=Re: ${selectedContact.subject}`, '_blank')
                    handleStatusUpdate(selectedContact._id, 'replied')
                    setSelectedContact(null)
                  }}
                >
                  <Reply className="h-4 w-4 mr-2" />
                  Reply via Email
                </Button>
                <Button
                  onClick={() => {
                    handleStatusUpdate(selectedContact._id, 'archived')
                    setSelectedContact(null)
                  }}
                >
                  <Archive className="h-4 w-4 mr-2" />
                  Archive
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AdminLayout>
  )
}
