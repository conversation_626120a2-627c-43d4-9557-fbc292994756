'use client'

import { motion } from 'framer-motion'
import { ArrowLeft, ExternalLink, Github, Calendar, Tag, Share2 } from 'lucide-react'
import Link from 'next/link'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/utils'

// This would normally come from an API call
const getProjectById = (id: string) => {
  const projects = [
    {
      id: '1',
      title: 'Smooth Beauty',
      description: 'Comprehensive beauty marketplace with user, vendor, admin, and delivery applications',
      longDescription: `Smooth Beauty is a comprehensive beauty marketplace platform that revolutionizes how customers discover and purchase beauty products. The platform consists of four interconnected applications:

**User Application**: A sleek, intuitive interface for customers to browse products, read reviews, make purchases, and track orders in real-time.

**Vendor Application**: A powerful dashboard for beauty brands and retailers to manage their inventory, process orders, track sales analytics, and communicate with customers.

**Admin Application**: A comprehensive management system for platform administrators to oversee all operations, manage users and vendors, handle disputes, and monitor platform performance.

**Delivery Application**: A mobile-optimized app for delivery personnel to receive assignments, navigate to destinations, and update delivery status in real-time.

The platform features advanced search and filtering capabilities, AI-powered product recommendations, secure payment processing with multiple payment gateways, and a robust review and rating system.`,
      technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'Socket.io', 'Stripe', 'JWT', 'AWS S3'],
      category: 'web',
      featured: true,
      images: ['/projects/smooth-beauty-1.jpg'],
      liveUrl: 'https://smoothbeauty.com',
      githubUrl: 'https://github.com/seyiobadeyi/smooth-beauty',
      demoUrl: 'https://demo.smoothbeauty.com',
      completedDate: new Date('2024-03-15'),
      challenges: [
        'Implementing real-time order tracking across multiple user types',
        'Designing a scalable architecture to handle high traffic during sales events',
        'Creating a unified design system across four different applications',
        'Integrating multiple payment gateways for international transactions'
      ],
      features: [
        'Real-time order tracking and notifications',
        'Multi-vendor marketplace with commission management',
        'Advanced search with AI-powered recommendations',
        'Secure payment processing with fraud detection',
        'Comprehensive admin dashboard with analytics',
        'Mobile-responsive design across all applications',
        'Multi-language and currency support',
        'Automated inventory management'
      ]
    }
    // Add other projects here...
  ]
  
  return projects.find(p => p.id === id)
}

export default function ProjectDetailPage() {
  const params = useParams()
  const router = useRouter()
  const project = getProjectById(params.id as string)

  if (!project) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Project Not Found</h1>
            <p className="text-muted-foreground mb-8">The project you're looking for doesn't exist.</p>
            <Button asChild>
              <Link href="/projects">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Link>
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: project.title,
        text: project.description,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
    }
  }

  return (
    <MainLayout>
      <div className="min-h-screen py-12">
        <div className="container-custom max-w-4xl">
          {/* Back Button */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <Button variant="ghost" asChild>
              <Link href="/projects">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Link>
            </Button>
          </motion.div>

          {/* Project Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-12"
          >
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 mb-8">
              <div>
                <h1 className="text-4xl md:text-5xl font-bold mb-4">
                  {project.title}
                  {project.featured && (
                    <span className="ml-3 px-3 py-1 bg-primary text-primary-foreground rounded-full text-sm font-medium">
                      Featured
                    </span>
                  )}
                </h1>
                <p className="text-xl text-muted-foreground">
                  {project.description}
                </p>
              </div>
              
              <div className="flex gap-3">
                {project.liveUrl && (
                  <Button asChild size="lg">
                    <Link href={project.liveUrl} target="_blank">
                      <ExternalLink className="h-5 w-5 mr-2" />
                      Live Demo
                    </Link>
                  </Button>
                )}
                {project.githubUrl && (
                  <Button asChild variant="outline" size="lg">
                    <Link href={project.githubUrl} target="_blank">
                      <Github className="h-5 w-5 mr-2" />
                      View Code
                    </Link>
                  </Button>
                )}
                <Button variant="outline" size="lg" onClick={handleShare}>
                  <Share2 className="h-5 w-5 mr-2" />
                  Share
                </Button>
              </div>
            </div>

            {/* Project Meta */}
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Completed {project.completedDate.toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                <span className="capitalize">{project.category} Application</span>
              </div>
            </div>
          </motion.div>

          {/* Project Image */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-12"
          >
            <div className="relative overflow-hidden rounded-lg bg-muted h-64 md:h-96">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                <div className="text-8xl font-bold text-primary/30">
                  {project.title.charAt(0)}
                </div>
              </div>
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Description */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Project Overview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-neutral dark:prose-invert max-w-none">
                      {project.longDescription.split('\n\n').map((paragraph, index) => (
                        <p key={index} className="mb-4 text-muted-foreground leading-relaxed">
                          {paragraph}
                        </p>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Features */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Key Features</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {project.features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                          <span className="text-muted-foreground">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Challenges */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Technical Challenges</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {project.challenges.map((challenge, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-destructive rounded-full mt-2 flex-shrink-0" />
                          <span className="text-muted-foreground">{challenge}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Technologies */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Technologies Used</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech) => (
                        <span
                          key={tech}
                          className="px-3 py-1 bg-muted text-muted-foreground rounded-full text-sm hover:bg-primary hover:text-primary-foreground transition-colors"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Project Links */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Project Links</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {project.liveUrl && (
                      <Button asChild variant="outline" className="w-full justify-start">
                        <Link href={project.liveUrl} target="_blank">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Live Website
                        </Link>
                      </Button>
                    )}
                    {project.githubUrl && (
                      <Button asChild variant="outline" className="w-full justify-start">
                        <Link href={project.githubUrl} target="_blank">
                          <Github className="h-4 w-4 mr-2" />
                          Source Code
                        </Link>
                      </Button>
                    )}
                    {project.demoUrl && (
                      <Button asChild variant="outline" className="w-full justify-start">
                        <Link href={project.demoUrl} target="_blank">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Demo Version
                        </Link>
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
