/**
 * Admin Dashboard Page
 * 
 * Main dashboard providing overview of site metrics, recent activities,
 * and quick access to key administrative functions.
 */

'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import {
  BarChart3,
  TrendingUp,
  Users,
  FileText,
  FolderOpen,
  Mail,
  Eye,
  Calendar,
  Activity,
  AlertCircle,
} from 'lucide-react'
import Link from 'next/link'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useApi } from '@/hooks/use-api'
import type { DashboardMetrics, RecentActivity } from '@/types/admin'

/**
 * Dashboard metrics card component
 */
interface MetricCardProps {
  title: string
  value: string | number
  change?: string
  changeType?: 'positive' | 'negative' | 'neutral'
  icon: React.ComponentType<{ className?: string }>
  href?: string
}

function MetricCard({ title, value, change, changeType = 'neutral', icon: Icon, href }: MetricCardProps) {
  const content = (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <p className={`text-xs ${
            changeType === 'positive' ? 'text-green-600' : 
            changeType === 'negative' ? 'text-red-600' : 
            'text-muted-foreground'
          }`}>
            {change}
          </p>
        )}
      </CardContent>
    </Card>
  )

  return href ? <Link href={href}>{content}</Link> : content
}

/**
 * Recent activity item component
 */
interface ActivityItemProps {
  activity: RecentActivity
}

function ActivityItem({ activity }: ActivityItemProps) {
  const getStatusColor = (status: RecentActivity['status']) => {
    switch (status) {
      case 'new': return 'bg-blue-500'
      case 'updated': return 'bg-yellow-500'
      case 'published': return 'bg-green-500'
      case 'archived': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const getTypeIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'contact': return Mail
      case 'newsletter': return Users
      case 'blog': return FileText
      case 'project': return FolderOpen
      default: return Activity
    }
  }

  const Icon = getTypeIcon(activity.type)

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-muted/50 rounded-lg transition-colors">
      <div className="flex-shrink-0">
        <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
          <Icon className="h-4 w-4" />
        </div>
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-foreground">{activity.title}</p>
        <p className="text-sm text-muted-foreground">{activity.description}</p>
        <div className="flex items-center mt-1 space-x-2">
          <div className={`w-2 h-2 rounded-full ${getStatusColor(activity.status)}`} />
          <span className="text-xs text-muted-foreground capitalize">{activity.status}</span>
          <span className="text-xs text-muted-foreground">
            {new Date(activity.timestamp).toLocaleDateString()}
          </span>
        </div>
      </div>
    </div>
  )
}

/**
 * Admin Dashboard Page Component
 */
export default function AdminDashboardPage(): JSX.Element {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const { data: statsResponse, loading: statsLoading, error: statsError } = useApi<{
    success: boolean
    data: {
      totalProjects: number
      totalBlogPosts: number
      totalContacts: number
      totalNewsletterSubscribers: number
      publishedBlogPosts: number
      draftBlogPosts: number
      unreadContacts: number
      activeSubscribers: number
    }
  }>('/api/admin/stats')

  const statsData = statsResponse?.data

  // Mock recent activities - in real app, this would come from API
  useEffect(() => {
    const mockActivities: RecentActivity[] = [
      {
        id: '1',
        type: 'contact',
        title: 'New Contact Message',
        description: 'John Doe sent a project inquiry',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        status: 'new',
      },
      {
        id: '2',
        type: 'blog',
        title: 'Blog Post Published',
        description: 'Building Modern Web Applications with Next.js',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
        status: 'published',
      },
      {
        id: '3',
        type: 'project',
        title: 'Project Updated',
        description: 'Smooth Beauty project details updated',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        status: 'updated',
      },
      {
        id: '4',
        type: 'newsletter',
        title: 'New Newsletter Subscriber',
        description: '<EMAIL> subscribed to newsletter',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        status: 'new',
      },
    ]
    setRecentActivities(mockActivities)
  }, [])

  // Process metrics from API data
  useEffect(() => {
    if (statsData) {
      setMetrics({
        totalProjects: statsData.totalProjects,
        totalBlogPosts: statsData.totalBlogPosts,
        totalContacts: statsData.totalContacts,
        totalNewsletterSubscribers: statsData.totalNewsletterSubscribers,
        publishedBlogPosts: statsData.publishedBlogPosts,
        draftBlogPosts: statsData.draftBlogPosts,
        unreadContacts: statsData.unreadContacts,
        activeSubscribers: statsData.activeSubscribers,
      })
    }
  }, [statsData])

  if (statsError) {
    return (
      <AdminLayout title="Dashboard">
        <Card className="p-6">
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            <p>Failed to load dashboard data. Please try again later.</p>
          </div>
        </Card>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout title="Dashboard">
      <div className="space-y-6">
        {/* Metrics Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <MetricCard
              title="Total Projects"
              value={statsLoading ? '...' : metrics?.totalProjects || 0}
              change="+2 this month"
              changeType="positive"
              icon={FolderOpen}
              href="/admin/projects"
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <MetricCard
              title="Blog Posts"
              value={statsLoading ? '...' : metrics?.totalBlogPosts || 0}
              change="+1 this week"
              changeType="positive"
              icon={FileText}
              href="/admin/blog"
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <MetricCard
              title="Contact Messages"
              value={statsLoading ? '...' : metrics?.totalContacts || 0}
              change={`${metrics?.unreadContacts || 0} unread`}
              changeType="neutral"
              icon={Mail}
              href="/admin/contacts"
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <MetricCard
              title="Newsletter Subscribers"
              value={statsLoading ? '...' : metrics?.totalNewsletterSubscribers || 0}
              change="+5 this week"
              changeType="positive"
              icon={Users}
              href="/admin/newsletter"
            />
          </motion.div>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {recentActivities.map((activity) => (
                    <ActivityItem key={activity.id} activity={activity} />
                  ))}
                </div>
                <div className="p-3 border-t">
                  <Button variant="outline" size="sm" className="w-full">
                    View All Activity
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button asChild className="w-full justify-start">
                  <Link href="/admin/projects/new">
                    <FolderOpen className="h-4 w-4 mr-2" />
                    Add New Project
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/admin/blog/new">
                    <FileText className="h-4 w-4 mr-2" />
                    Write Blog Post
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/admin/contacts">
                    <Mail className="h-4 w-4 mr-2" />
                    Review Messages
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/admin/settings">
                    <Activity className="h-4 w-4 mr-2" />
                    Site Settings
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* System Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                  <div>
                    <p className="text-sm font-medium">Database</p>
                    <p className="text-xs text-muted-foreground">Connected</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                  <div>
                    <p className="text-sm font-medium">Email Service</p>
                    <p className="text-xs text-muted-foreground">Operational</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                  <div>
                    <p className="text-sm font-medium">File Storage</p>
                    <p className="text-xs text-muted-foreground">Available</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </AdminLayout>
  )
}
