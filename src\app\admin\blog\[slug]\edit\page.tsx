/**
 * Admin Edit Blog Post Page
 * 
 * Form for editing existing blog posts with rich text editing,
 * metadata management, and publishing controls.
 */

'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  Save,
  Eye,
  Globe,
  ArrowLeft,
  Tag,
  Calendar,
  FileText,
  Image,
  Link as LinkIcon,
  Trash2,
} from 'lucide-react'
import Link from 'next/link'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { useApi, useApiMutation } from '@/hooks/use-api'
import { toast } from 'react-hot-toast'
import type { IBlogPost } from '@/types'

interface BlogPostFormData {
  title: string
  slug: string
  excerpt: string
  content: string
  tags: string[]
  published: boolean
  publishedAt?: Date
  metaTitle?: string
  metaDescription?: string
  featuredImage?: string
}

interface EditBlogPostPageProps {
  params: {
    slug: string
  }
}

/**
 * Admin Edit Blog Post Page Component
 */
export default function AdminEditBlogPostPage({ params }: EditBlogPostPageProps): JSX.Element {
  const router = useRouter()
  const [formData, setFormData] = useState<BlogPostFormData>({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    tags: [],
    published: false,
  })
  const [tagInput, setTagInput] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  // API hooks
  const { data: postData, loading: loadingPost } = useApi<{
    success: boolean
    data: IBlogPost
  }>(`/api/blog/${params.slug}`)
  
  const { mutate: updatePost, loading: updating } = useApiMutation()
  const { mutate: deletePost, loading: deleting } = useApiMutation()

  // Load post data when available
  useEffect(() => {
    if (postData?.data) {
      const post = postData.data
      setFormData({
        title: post.title,
        slug: post.slug,
        excerpt: post.excerpt || '',
        content: post.content,
        tags: post.tags || [],
        published: post.published,
        publishedAt: post.publishedAt ? new Date(post.publishedAt) : undefined,
        metaTitle: post.metaTitle,
        metaDescription: post.metaDescription,
        featuredImage: post.featuredImage,
      })
    }
  }, [postData])

  // Generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }

  // Handle form field changes
  const handleInputChange = (field: keyof BlogPostFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      // Auto-generate slug when title changes (but keep original if editing)
      ...(field === 'title' && !params.slug && { slug: generateSlug(value) })
    }))
    
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // Handle tag addition
  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault()
      const newTag = tagInput.trim().toLowerCase()
      if (!formData.tags.includes(newTag)) {
        handleInputChange('tags', [...formData.tags, newTag])
      }
      setTagInput('')
    }
  }

  // Handle tag removal
  const handleRemoveTag = (tagToRemove: string) => {
    handleInputChange('tags', formData.tags.filter(tag => tag !== tagToRemove))
  }

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug is required'
    }

    if (!formData.excerpt.trim()) {
      newErrors.excerpt = 'Excerpt is required'
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Content is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (publish?: boolean) => {
    if (!validateForm()) {
      toast.error('Please fix the form errors')
      return
    }

    try {
      const postData = {
        ...formData,
        published: publish !== undefined ? publish : formData.published,
        publishedAt: publish && !formData.published ? new Date() : formData.publishedAt,
      }

      await updatePost(`/api/blog/${params.slug}`, postData, 'PUT')
      
      toast.success('Blog post updated successfully!')
      router.push('/admin/blog')
    } catch (error) {
      toast.error('Failed to update blog post')
    }
  }

  // Handle post deletion
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
      return
    }

    try {
      await deletePost(`/api/blog/${params.slug}`, {}, 'DELETE')
      toast.success('Blog post deleted successfully')
      router.push('/admin/blog')
    } catch (error) {
      toast.error('Failed to delete blog post')
    }
  }

  // Show loading state
  if (loadingPost) {
    return (
      <AdminLayout title="Loading...">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>
      </AdminLayout>
    )
  }

  // Show error if post not found
  if (!postData?.data) {
    return (
      <AdminLayout title="Post Not Found">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Blog Post Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The blog post you're looking for doesn't exist or has been deleted.
          </p>
          <Button asChild>
            <Link href="/admin/blog">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Blog Posts
            </Link>
          </Button>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout 
      title={`Edit: ${formData.title}`}
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Blog Posts', href: '/admin/blog' },
        { label: 'Edit Post' },
      ]}
    >
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/admin/blog">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h2 className="text-2xl font-bold tracking-tight">Edit Blog Post</h2>
              <p className="text-muted-foreground">
                Update your blog content and settings
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleting}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSubmit()}
              disabled={updating}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
            {!formData.published && (
              <Button
                onClick={() => handleSubmit(true)}
                disabled={updating}
              >
                <Globe className="h-4 w-4 mr-2" />
                Publish
              </Button>
            )}
            {formData.published && (
              <Button
                variant="outline"
                onClick={() => handleSubmit(false)}
                disabled={updating}
              >
                Unpublish
              </Button>
            )}
          </div>
        </motion.div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2 space-y-6"
          >
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Post Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Title *</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter post title..."
                    className={errors.title ? 'border-destructive' : ''}
                  />
                  {errors.title && (
                    <p className="text-sm text-destructive mt-1">{errors.title}</p>
                  )}
                </div>

                <div>
                  <label className="text-sm font-medium">Slug *</label>
                  <Input
                    value={formData.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                    placeholder="post-url-slug"
                    className={errors.slug ? 'border-destructive' : ''}
                  />
                  {errors.slug && (
                    <p className="text-sm text-destructive mt-1">{errors.slug}</p>
                  )}
                  <p className="text-xs text-muted-foreground mt-1">
                    URL: /blog/{formData.slug || 'post-slug'}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium">Excerpt *</label>
                  <Textarea
                    value={formData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    placeholder="Brief description of the post..."
                    rows={3}
                    className={errors.excerpt ? 'border-destructive' : ''}
                  />
                  {errors.excerpt && (
                    <p className="text-sm text-destructive mt-1">{errors.excerpt}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Content Editor */}
            <Card>
              <CardHeader>
                <CardTitle>Content *</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  placeholder="Write your blog post content here... (Markdown supported)"
                  rows={20}
                  className={`font-mono ${errors.content ? 'border-destructive' : ''}`}
                />
                {errors.content && (
                  <p className="text-sm text-destructive mt-1">{errors.content}</p>
                )}
                <p className="text-xs text-muted-foreground mt-2">
                  Supports Markdown formatting. Use **bold**, *italic*, `code`, and more.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Publishing Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Publishing Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant={formData.published ? 'default' : 'secondary'}>
                    {formData.published ? 'Published' : 'Draft'}
                  </Badge>
                </div>

                {formData.published && formData.publishedAt && (
                  <div>
                    <span className="text-sm font-medium">Published:</span>
                    <p className="text-sm text-muted-foreground">
                      {new Date(formData.publishedAt).toLocaleDateString()} at{' '}
                      {new Date(formData.publishedAt).toLocaleTimeString()}
                    </p>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="published"
                    checked={formData.published}
                    onCheckedChange={(checked) => handleInputChange('published', checked)}
                  />
                  <label htmlFor="published" className="text-sm font-medium">
                    Published
                  </label>
                </div>
              </CardContent>
            </Card>

            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="h-5 w-5" />
                  Tags
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleAddTag}
                    placeholder="Add tags (press Enter)"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Press Enter to add tags
                  </p>
                </div>

                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag) => (
                      <Badge
                        key={tag}
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => handleRemoveTag(tag)}
                      >
                        {tag} ×
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => window.open(`/blog/${formData.slug}`, '_blank')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {formData.published ? 'View Live Post' : 'Preview Draft'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    navigator.clipboard.writeText(`${window.location.origin}/blog/${formData.slug}`)
                    toast.success('Link copied to clipboard')
                  }}
                >
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Copy Link
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </AdminLayout>
  )
}
